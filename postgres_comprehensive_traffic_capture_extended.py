#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
PostgreSQL全面流量抓包脚本 (Extended Query协议版本)
基于PostgreSQL官方文档创建的全面测试用例
使用Extended Query协议 (Parse/Bind/Execute) 替代Simple Query协议
涵盖所有主要的SQL命令、数据类型、函数和操作符

协议对比:
- Simple Query: 直接发送SQL文本 -> 服务器解析执行
- Extended Query: Parse(解析) -> Bind(绑定参数) -> Execute(执行)

Extended Query优势:
- 支持参数化查询，防止SQL注入
- 服务器端语句缓存，提高性能
- 支持二进制数据传输
- 更精细的错误处理
"""
import psycopg2
import psycopg2.extensions
import paramiko
import time
import os
from datetime import datetime, date, timedelta
import sys
from pathlib import Path
import subprocess
import logging
import json
import uuid
import decimal
from typing import List, Dict, Any

# 配置日志
logging.basicConfig(
    level=logging.INFO,
    format='%(asctime)s - %(levelname)s - %(message)s'
)
logger = logging.getLogger(__name__)

# 服务器配置
SERVER_CONFIG = {
    'hostname': '**************',
    'username': 'root',
    'password': 'root@123',
    'port': 22
}

# 数据库配置
DB_CONFIG = {
    'host': '**************',
    'port': 5432,
    'database': 'postgres',
    'user': 'postgres',
    'password': 'mysecretpassword'
}

# 抓包配置
CAPTURE_CONFIG = {
    'interface': 'eth0',
    'port': 5432,
    'remote_dir': '/root/pcap',
    'local_dir': './postgres_comprehensive_extended_pcap'
}

class ComprehensiveTrafficCaptureExtended:
    def __init__(self):
        self.ssh = None
        self.sftp = None
        self.connect_ssh()
        self.setup_directories()
        self.test_results = []
        
    def connect_ssh(self):
        """连接SSH服务器"""
        try:
            self.ssh = paramiko.SSHClient()
            self.ssh.set_missing_host_key_policy(paramiko.AutoAddPolicy())
            self.ssh.connect(**SERVER_CONFIG)
            self.sftp = self.ssh.open_sftp()
            logger.info("SSH连接成功")
        except Exception as e:
            logger.error(f"SSH连接失败: {e}")
            sys.exit(1)

    def setup_directories(self):
        """设置远程和本地目录"""
        self.ssh.exec_command(f"mkdir -p {CAPTURE_CONFIG['remote_dir']}")
        Path(CAPTURE_CONFIG['local_dir']).mkdir(parents=True, exist_ok=True)

    def start_capture(self, capture_type):
        """开始抓包"""
        timestamp = datetime.now().strftime('%Y%m%d_%H%M%S')
        remote_file = f"{CAPTURE_CONFIG['remote_dir']}/pg_ext_{capture_type}_{timestamp}.pcap"
        cmd = f"tcpdump -i {CAPTURE_CONFIG['interface']} -s 0 -w {remote_file} port {CAPTURE_CONFIG['port']}"
        logger.info(f"开始抓包: {capture_type} (Extended Query协议)")
        self.ssh.exec_command(f"nohup {cmd} > /dev/null 2>&1 &")
        time.sleep(2)
        return remote_file

    def stop_capture(self):
        """停止抓包"""
        logger.info("停止抓包...")
        self.ssh.exec_command("pkill tcpdump")
        time.sleep(2)

    def download_pcap(self, remote_file):
        """下载pcap文件到本地"""
        filename = os.path.basename(remote_file)
        local_file = os.path.join(CAPTURE_CONFIG['local_dir'], filename)
        self.sftp.get(remote_file, local_file)
        logger.info(f"已下载: {filename}")
        return local_file

    def execute_test_with_capture(self, test_name: str, test_func):
        """执行测试并抓包"""
        logger.info(f"\n=== 开始 {test_name} 测试 (Extended Query) ===")
        remote_file = self.start_capture(test_name.lower().replace(' ', '_'))
        
        try:
            operations = test_func()
            local_file = self.download_pcap(remote_file)
            
            self.test_results.append({
                'test_name': test_name,
                'pcap_file': local_file,
                'operations': operations,
                'status': 'SUCCESS',
                'timestamp': datetime.now().isoformat()
            })
            logger.info(f"{test_name} 测试完成")
            
        except Exception as e:
            logger.error(f"{test_name} 测试失败: {e}")
            self.stop_capture()
            try:
                local_file = self.download_pcap(remote_file)
                self.test_results.append({
                    'test_name': test_name,
                    'pcap_file': local_file,
                    'operations': [],
                    'status': 'FAILED',
                    'error': str(e),
                    'timestamp': datetime.now().isoformat()
                })
            except:
                pass

    def safe_commit_and_autocommit(self, conn):
        """安全地提交事务并设置自动提交模式"""
        try:
            conn.commit()
        except:
            pass
        conn.autocommit = True

    def safe_set_manual_commit(self, conn):
        """安全地设置手动提交模式"""
        conn.autocommit = False

    def test_ddl_operations(self):
        """测试DDL操作 - Extended Query协议（DDL不支持PREPARE，使用参数化查询模拟）"""
        conn = psycopg2.connect(**DB_CONFIG)
        cur = conn.cursor()
        operations = []

        try:
            # DDL操作 - 直接执行（DDL不支持PREPARE）
            operations.append("DDL操作 - Extended Query风格")

            # 创建模式
            cur.execute("CREATE SCHEMA test_schema")
            operations.append("CREATE SCHEMA test_schema")

            # 创建域
            cur.execute("CREATE DOMAIN test_domain AS INTEGER CHECK (VALUE > 0)")
            operations.append("CREATE DOMAIN test_domain")

            # 创建枚举类型
            cur.execute("CREATE TYPE mood AS ENUM ('sad', 'ok', 'happy')")
            operations.append("CREATE TYPE mood ENUM")

            # 创建复合类型
            cur.execute("CREATE TYPE complex AS (r double precision, i double precision)")
            operations.append("CREATE TYPE complex")

            # 创建表
            cur.execute("""
                CREATE TABLE test_schema.comprehensive_test (
                    id SERIAL PRIMARY KEY,
                    name VARCHAR(100) NOT NULL,
                    age test_domain,
                    mood_state mood,
                    complex_num complex,
                    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP
                )
            """)
            operations.append("CREATE TABLE comprehensive_test")

            # 创建索引
            cur.execute("CREATE INDEX idx_name ON test_schema.comprehensive_test(name)")
            operations.append("CREATE INDEX idx_name")

            # 创建视图
            cur.execute("""
                CREATE VIEW test_schema.test_view AS
                SELECT id, name, age FROM test_schema.comprehensive_test WHERE age > 18
            """)
            operations.append("CREATE VIEW test_view")

            # 修改表结构
            cur.execute("ALTER TABLE test_schema.comprehensive_test ADD COLUMN email VARCHAR(255)")
            operations.append("ALTER TABLE ADD COLUMN")

            # 使用Extended Query进行数据操作
            operations.append("使用Extended Query进行数据操作")

            # 预编译插入语句 - Extended Query
            cur.execute("""
                PREPARE insert_test_stmt (VARCHAR(100), INTEGER, mood, complex) AS
                INSERT INTO test_schema.comprehensive_test (name, age, mood_state, complex_num)
                VALUES ($1, $2, $3, $4)
            """)

            # 执行插入 - Extended Query
            cur.execute("EXECUTE insert_test_stmt (%s, %s, %s, %s)",
                       ('Test User', 25, 'happy', '(1.5,2.5)'))
            operations.append("INSERT with Extended Query")

            # 预编译查询语句 - Extended Query
            cur.execute("""
                PREPARE select_test_stmt (INTEGER) AS
                SELECT * FROM test_schema.comprehensive_test WHERE age > $1
            """)

            cur.execute("EXECUTE select_test_stmt (%s)", (20,))
            results = cur.fetchall()
            logger.info(f"Extended Query查询返回 {len(results)} 行")
            operations.append("SELECT with Extended Query")

            conn.commit()

            # 清理 - 直接执行DDL
            try:
                cur.execute("DEALLOCATE insert_test_stmt")
                cur.execute("DEALLOCATE select_test_stmt")

                cur.execute("DROP SCHEMA test_schema CASCADE")
                cur.execute("DROP TYPE mood CASCADE")
                cur.execute("DROP TYPE complex CASCADE")
                cur.execute("DROP DOMAIN test_domain CASCADE")

                conn.commit()
                operations.append("清理DDL对象")
            except:
                pass

            cur.close()
            conn.close()

            # 在关闭数据库连接后停止抓包
            time.sleep(3)
            self.stop_capture()

            return operations

        except Exception as e:
            # 发生异常时也要关闭连接和停止抓包
            try:
                if 'cur' in locals():
                    cur.close()
                if 'conn' in locals():
                    conn.close()
                time.sleep(3)
                self.stop_capture()
            except:
                pass
            raise e

    def test_data_types(self):
        """测试各种数据类型 - Extended Query协议"""
        conn = psycopg2.connect(**DB_CONFIG)
        cur = conn.cursor()
        operations = []

        try:
            # 创建包含所有数据类型的表 - 直接执行（CREATE TABLE不支持PREPARE）
            operations.append("创建数据类型测试表 - Extended Query")
            cur.execute("""
                CREATE TABLE data_types_test (
                    -- 数值类型
                    col_smallint SMALLINT,
                    col_integer INTEGER,
                    col_bigint BIGINT,
                    col_decimal DECIMAL(10,2),
                    col_numeric NUMERIC(15,5),
                    col_real REAL,
                    col_double DOUBLE PRECISION,
                    col_smallserial SMALLSERIAL,
                    col_serial SERIAL,
                    col_bigserial BIGSERIAL,

                    -- 字符类型
                    col_char CHAR(10),
                    col_varchar VARCHAR(100),
                    col_text TEXT,

                    -- 二进制类型
                    col_bytea BYTEA,

                    -- 日期时间类型
                    col_date DATE,
                    col_time TIME,
                    col_timetz TIME WITH TIME ZONE,
                    col_timestamp TIMESTAMP,
                    col_timestamptz TIMESTAMP WITH TIME ZONE,
                    col_interval INTERVAL,

                    -- 布尔类型
                    col_boolean BOOLEAN,

                    -- 几何类型
                    col_point POINT,
                    col_line LINE,
                    col_lseg LSEG,
                    col_box BOX,
                    col_path PATH,
                    col_polygon POLYGON,
                    col_circle CIRCLE,

                    -- 网络地址类型
                    col_cidr CIDR,
                    col_inet INET,
                    col_macaddr MACADDR,

                    -- 位串类型
                    col_bit BIT(8),
                    col_varbit BIT VARYING(16),

                    -- UUID类型
                    col_uuid UUID,

                    -- JSON类型
                    col_json JSON,
                    col_jsonb JSONB,

                    -- 数组类型
                    col_int_array INTEGER[],
                    col_text_array TEXT[],

                    -- 范围类型
                    col_int4range INT4RANGE,
                    col_tsrange TSRANGE
                )
            """)

            # 预编译插入语句 - Extended Query
            operations.append("预编译插入语句 - Extended Query")
            cur.execute("""
                PREPARE insert_datatypes_stmt (
                    SMALLINT, INTEGER, BIGINT, DECIMAL, NUMERIC,
                    REAL, DOUBLE PRECISION, CHAR, VARCHAR, TEXT,
                    BYTEA, DATE, TIME, TIMESTAMP, INTERVAL,
                    BOOLEAN, POINT, BOX, CIDR, INET,
                    MACADDR, BIT, VARBIT, UUID, JSON, JSONB,
                    INTEGER[], TEXT[], INT4RANGE
                ) AS
                INSERT INTO data_types_test (
                    col_smallint, col_integer, col_bigint, col_decimal, col_numeric,
                    col_real, col_double, col_char, col_varchar, col_text,
                    col_bytea, col_date, col_time, col_timestamp, col_interval,
                    col_boolean, col_point, col_box, col_cidr, col_inet,
                    col_macaddr, col_bit, col_varbit, col_uuid, col_json, col_jsonb,
                    col_int_array, col_text_array, col_int4range
                ) VALUES (
                    $1, $2, $3, $4, $5, $6, $7, $8, $9, $10,
                    $11, $12, $13, $14, $15, $16, $17, $18, $19, $20,
                    $21, $22, $23, $24, $25, $26, $27, $28, $29
                )
            """)

            # 执行插入 - Extended Query
            operations.append("执行参数化插入 - Extended Query")
            cur.execute("""
                EXECUTE insert_datatypes_stmt (
                    %s, %s, %s, %s, %s, %s, %s, %s, %s, %s,
                    %s, %s, %s, %s, %s, %s, %s, %s, %s, %s,
                    %s, %s, %s, %s, %s, %s, %s, %s, %s
                )
            """, (
                32767, 2147483647, 9223372036854775807, 12345.67, 12345.67890,
                123.45, 123456.789012, 'test      ', 'test varchar', 'test text',
                b'\\xDEADBEEF', '2023-12-25', '14:30:00', '2023-12-25 14:30:00', '1 year 2 months',
                True, '(1,2)', '((0,0),(1,1))', '192.168.1.0/24', '192.168.1.1',
                '08:00:2b:01:02:03', '10101010', '1010101011110000',
                'a0eebc99-9c0b-4ef8-bb6d-6bb9bd380a11',
                '{"name": "test", "value": 123}', '{"name": "test", "value": 123}',
                [1,2,3,4,5], ['apple','banana','cherry'], '[1,10)'
            ))

            # 预编译查询语句 - Extended Query
            operations.append("预编译查询语句 - Extended Query")
            cur.execute("PREPARE select_datatypes_stmt AS SELECT * FROM data_types_test")
            cur.execute("EXECUTE select_datatypes_stmt")
            result = cur.fetchone()
            logger.info(f"数据类型测试结果: {len(result)} 列数据")
            cur.execute("DEALLOCATE select_datatypes_stmt")

            # 预编译特定类型查询 - Extended Query
            operations.append("预编译特定类型查询 - Extended Query")
            cur.execute("""
                PREPARE select_json_stmt AS
                SELECT col_json->>'name' as json_name, col_jsonb->>'value' as jsonb_value
                FROM data_types_test
            """)
            cur.execute("EXECUTE select_json_stmt")
            json_result = cur.fetchone()
            logger.info(f"JSON查询结果: {json_result}")
            cur.execute("DEALLOCATE select_json_stmt")

            # 预编译数组查询 - Extended Query
            cur.execute("""
                PREPARE select_array_stmt AS
                SELECT col_int_array[1] as first_int, array_length(col_text_array, 1) as text_array_length
                FROM data_types_test
            """)
            cur.execute("EXECUTE select_array_stmt")
            array_result = cur.fetchone()
            logger.info(f"数组查询结果: {array_result}")
            cur.execute("DEALLOCATE select_array_stmt")

            conn.commit()

            # 清理 - Extended Query
            try:
                cur.execute("DROP TABLE data_types_test")
                cur.execute("DEALLOCATE insert_datatypes_stmt")
                conn.commit()
                operations.append("清理数据类型测试表 - Extended Query")
            except:
                pass

            cur.close()
            conn.close()

            # 在关闭数据库连接后停止抓包
            time.sleep(3)
            self.stop_capture()

            return operations

        except Exception as e:
            # 发生异常时也要关闭连接和停止抓包
            try:
                if 'cur' in locals():
                    cur.close()
                if 'conn' in locals():
                    conn.close()
                time.sleep(3)
                self.stop_capture()
            except:
                pass
            raise e

    def test_dml_operations(self):
        """测试DML操作 - Extended Query协议"""
        conn = psycopg2.connect(**DB_CONFIG)
        cur = conn.cursor()
        operations = []

        try:
            # 创建测试表 - 直接执行（CREATE TABLE不支持PREPARE）
            operations.append("创建DML测试表 - Extended Query")
            cur.execute("""
                CREATE TABLE dml_test (
                    id SERIAL PRIMARY KEY,
                    name VARCHAR(100),
                    age INTEGER,
                    salary DECIMAL(10,2),
                    department VARCHAR(50),
                    hire_date DATE,
                    is_active BOOLEAN DEFAULT true
                )
            """)

            # 预编译INSERT语句 - Extended Query
            operations.append("预编译INSERT语句 - Extended Query")
            cur.execute("""
                PREPARE insert_employee_stmt (VARCHAR(100), INTEGER, DECIMAL(10,2), VARCHAR(50), DATE) AS
                INSERT INTO dml_test (name, age, salary, department, hire_date)
                VALUES ($1, $2, $3, $4, $5)
            """)

            # 批量插入数据 - Extended Query
            operations.append("批量插入数据 - Extended Query")
            employees = [
                ('Alice Johnson', 30, 75000.00, 'Engineering', '2020-01-15'),
                ('Bob Smith', 25, 65000.00, 'Marketing', '2021-03-20'),
                ('Carol Davis', 35, 85000.00, 'Engineering', '2019-07-10'),
                ('David Wilson', 28, 70000.00, 'Sales', '2020-11-05'),
                ('Eve Brown', 32, 80000.00, 'HR', '2018-09-12')
            ]

            for name, age, salary, dept, hire_date in employees:
                cur.execute("EXECUTE insert_employee_stmt (%s, %s, %s, %s, %s)",
                           (name, age, salary, dept, hire_date))

            # 预编译UPDATE语句 - Extended Query
            operations.append("预编译UPDATE语句 - Extended Query")
            cur.execute("""
                PREPARE update_salary_stmt (DECIMAL(3,2), VARCHAR(50)) AS
                UPDATE dml_test SET salary = salary * $1 WHERE department = $2
            """)

            cur.execute("EXECUTE update_salary_stmt (%s, %s)", (1.1, 'Engineering'))
            updated_rows = cur.rowcount
            logger.info(f"更新了 {updated_rows} 行工程部门员工薪资")

            # 预编译条件UPDATE语句 - Extended Query
            cur.execute("""
                PREPARE update_status_stmt (INTEGER) AS
                UPDATE dml_test SET is_active = false WHERE age > $1
            """)

            cur.execute("EXECUTE update_status_stmt (%s)", (33,))
            deactivated_rows = cur.rowcount
            logger.info(f"停用了 {deactivated_rows} 名年龄超过33岁的员工")

            # 预编译DELETE语句 - Extended Query
            operations.append("预编译DELETE语句 - Extended Query")
            cur.execute("""
                PREPARE delete_inactive_stmt AS
                DELETE FROM dml_test WHERE is_active = false
            """)

            cur.execute("EXECUTE delete_inactive_stmt")
            deleted_rows = cur.rowcount
            logger.info(f"删除了 {deleted_rows} 名非活跃员工")

            # 预编译SELECT语句 - Extended Query
            operations.append("预编译SELECT语句 - Extended Query")
            cur.execute("""
                PREPARE select_by_dept_stmt (VARCHAR(50)) AS
                SELECT name, age, salary FROM dml_test WHERE department = $1 ORDER BY salary DESC
            """)

            cur.execute("EXECUTE select_by_dept_stmt (%s)", ('Engineering',))
            eng_employees = cur.fetchall()
            logger.info(f"工程部门员工: {len(eng_employees)} 人")

            # 预编译聚合查询 - Extended Query
            cur.execute("""
                PREPARE dept_stats_stmt AS
                SELECT department, COUNT(*) as emp_count, AVG(salary) as avg_salary, MAX(salary) as max_salary
                FROM dml_test
                GROUP BY department
                ORDER BY avg_salary DESC
            """)

            cur.execute("EXECUTE dept_stats_stmt")
            dept_stats = cur.fetchall()
            logger.info(f"部门统计: {len(dept_stats)} 个部门")

            conn.commit()

            # 清理预编译语句和表 - Extended Query
            try:
                cur.execute("DEALLOCATE insert_employee_stmt")
                cur.execute("DEALLOCATE update_salary_stmt")
                cur.execute("DEALLOCATE update_status_stmt")
                cur.execute("DEALLOCATE delete_inactive_stmt")
                cur.execute("DEALLOCATE select_by_dept_stmt")
                cur.execute("DEALLOCATE dept_stats_stmt")

                cur.execute("DROP TABLE IF EXISTS dml_test CASCADE")

                conn.commit()
                operations.append("清理DML测试对象 - Extended Query")
            except:
                pass

        except Exception as e:
            logger.error(f"DML操作测试失败: {e}")
            conn.rollback()
            raise e
        finally:
            # 关键：先关闭数据库连接，再停止抓包
            cur.close()
            conn.close()
            logger.info("数据库连接已关闭")

            time.sleep(3)  # 等待网络包传输完成
            self.stop_capture()

        return operations

    def test_complex_queries(self):
        """测试复杂查询 - Extended Query协议"""
        conn = psycopg2.connect(**DB_CONFIG)
        cur = conn.cursor()
        operations = []

        try:
            # 创建测试表 - Extended Query
            operations.append("创建复杂查询测试表 - Extended Query")
            cur.execute("""
                PREPARE create_employees_table_stmt AS
                CREATE TABLE employees (
                    id SERIAL PRIMARY KEY,
                    name VARCHAR(100),
                    department VARCHAR(50),
                    salary DECIMAL(10,2),
                    manager_id INTEGER REFERENCES employees(id),
                    hire_date DATE
                )
            """)
            cur.execute("EXECUTE create_employees_table_stmt")
            cur.execute("DEALLOCATE create_employees_table_stmt")

            cur.execute("""
                PREPARE create_departments_table_stmt AS
                CREATE TABLE departments (
                    id SERIAL PRIMARY KEY,
                    name VARCHAR(50),
                    budget DECIMAL(12,2)
                )
            """)
            cur.execute("EXECUTE create_departments_table_stmt")
            cur.execute("DEALLOCATE create_departments_table_stmt")

            # 预编译插入部门数据 - Extended Query
            operations.append("预编译插入部门数据 - Extended Query")
            cur.execute("""
                PREPARE insert_dept_stmt (VARCHAR(50), DECIMAL(12,2)) AS
                INSERT INTO departments (name, budget) VALUES ($1, $2)
            """)

            departments = [
                ('Engineering', 1000000.00),
                ('Marketing', 500000.00),
                ('Sales', 750000.00),
                ('HR', 300000.00)
            ]

            for dept_name, budget in departments:
                cur.execute("EXECUTE insert_dept_stmt (%s, %s)", (dept_name, budget))

            # 预编译插入员工数据 - Extended Query
            cur.execute("""
                PREPARE insert_emp_stmt (VARCHAR(100), VARCHAR(50), DECIMAL(10,2), INTEGER, DATE) AS
                INSERT INTO employees (name, department, salary, manager_id, hire_date)
                VALUES ($1, $2, $3, $4, $5)
            """)

            employees = [
                ('John CEO', 'Executive', 200000.00, None, '2015-01-01'),
                ('Alice Manager', 'Engineering', 120000.00, 1, '2016-03-15'),
                ('Bob Developer', 'Engineering', 80000.00, 2, '2018-06-20'),
                ('Carol Developer', 'Engineering', 85000.00, 2, '2017-09-10'),
                ('David Sales Manager', 'Sales', 110000.00, 1, '2016-11-05'),
                ('Eve Sales Rep', 'Sales', 60000.00, 5, '2019-02-14')
            ]

            for name, dept, salary, mgr_id, hire_date in employees:
                cur.execute("EXECUTE insert_emp_stmt (%s, %s, %s, %s, %s)",
                           (name, dept, salary, mgr_id, hire_date))

            # 预编译窗口函数查询 - Extended Query
            operations.append("预编译窗口函数查询 - Extended Query")
            cur.execute("""
                PREPARE window_query_stmt AS
                SELECT
                    name,
                    department,
                    salary,
                    ROW_NUMBER() OVER (PARTITION BY department ORDER BY salary DESC) as dept_rank,
                    LAG(salary) OVER (ORDER BY hire_date) as prev_salary,
                    SUM(salary) OVER (PARTITION BY department) as dept_total_salary
                FROM employees
            """)
            cur.execute("EXECUTE window_query_stmt")
            results = cur.fetchall()
            logger.info(f"窗口函数查询返回 {len(results)} 行")

            # 预编译CTE查询 - Extended Query
            operations.append("预编译CTE查询 - Extended Query")
            cur.execute("""
                PREPARE cte_query_stmt (DECIMAL(10,2)) AS
                WITH dept_stats AS (
                    SELECT
                        department,
                        COUNT(*) as emp_count,
                        AVG(salary) as avg_salary,
                        MAX(salary) as max_salary
                    FROM employees
                    GROUP BY department
                ),
                high_salary_depts AS (
                    SELECT department FROM dept_stats WHERE avg_salary > $1
                )
                SELECT e.name, e.department, e.salary
                FROM employees e
                JOIN high_salary_depts h ON e.department = h.department
                ORDER BY e.salary DESC
            """)
            cur.execute("EXECUTE cte_query_stmt (%s)", (70000,))
            results = cur.fetchall()
            logger.info(f"CTE查询返回 {len(results)} 行")

            # 预编译递归CTE查询 - Extended Query
            operations.append("预编译递归CTE查询 - Extended Query")
            cur.execute("""
                PREPARE recursive_cte_stmt AS
                WITH RECURSIVE employee_hierarchy AS (
                    -- 基础情况：顶级管理者
                    SELECT id, name, manager_id, 0 as level, CAST(name AS TEXT) as path
                    FROM employees
                    WHERE manager_id IS NULL

                    UNION ALL

                    -- 递归情况：下级员工
                    SELECT e.id, e.name, e.manager_id, eh.level + 1,
                           CAST(eh.path || ' -> ' || e.name AS TEXT) as path
                    FROM employees e
                    JOIN employee_hierarchy eh ON e.manager_id = eh.id
                )
                SELECT id, name, manager_id, level, path FROM employee_hierarchy ORDER BY level, name
            """)
            cur.execute("EXECUTE recursive_cte_stmt")
            results = cur.fetchall()
            logger.info(f"递归CTE查询返回 {len(results)} 行")

            # 预编译子查询 - Extended Query
            operations.append("预编译子查询 - Extended Query")
            cur.execute("""
                PREPARE subquery_stmt AS
                SELECT name, salary,
                    (SELECT AVG(salary) FROM employees WHERE department = e.department) as dept_avg
                FROM employees e
                WHERE salary > (SELECT AVG(salary) FROM employees)
            """)
            cur.execute("EXECUTE subquery_stmt")
            results = cur.fetchall()
            logger.info(f"子查询返回 {len(results)} 行")

            conn.commit()

            # 清理预编译语句和表 - Extended Query
            try:
                cur.execute("DEALLOCATE insert_dept_stmt")
                cur.execute("DEALLOCATE insert_emp_stmt")
                cur.execute("DEALLOCATE window_query_stmt")
                cur.execute("DEALLOCATE cte_query_stmt")
                cur.execute("DEALLOCATE recursive_cte_stmt")
                cur.execute("DEALLOCATE subquery_stmt")

                cur.execute("PREPARE drop_employees_stmt AS DROP TABLE IF EXISTS employees CASCADE")
                cur.execute("EXECUTE drop_employees_stmt")
                cur.execute("DEALLOCATE drop_employees_stmt")

                cur.execute("PREPARE drop_departments_stmt AS DROP TABLE IF EXISTS departments CASCADE")
                cur.execute("EXECUTE drop_departments_stmt")
                cur.execute("DEALLOCATE drop_departments_stmt")

                conn.commit()
                operations.append("清理复杂查询测试对象 - Extended Query")
            except:
                pass

            cur.close()
            conn.close()

            # 在关闭数据库连接后停止抓包
            time.sleep(3)
            self.stop_capture()

            return operations

        except Exception as e:
            # 发生异常时也要关闭连接和停止抓包
            try:
                if 'cur' in locals():
                    cur.close()
                if 'conn' in locals():
                    conn.close()
                time.sleep(3)
                self.stop_capture()
            except:
                pass
            raise e

    def test_functions_and_operators(self):
        """测试函数和操作符 - Extended Query协议"""
        conn = psycopg2.connect(**DB_CONFIG)
        cur = conn.cursor()
        operations = []

        try:
            # 预编译数学函数测试 - Extended Query
            operations.append("预编译数学函数测试 - Extended Query")
            cur.execute("""
                PREPARE math_functions_stmt (INTEGER, DECIMAL(5,3), DECIMAL(5,3), DECIMAL(5,3), INTEGER, INTEGER, INTEGER) AS
                SELECT ABS($1), CEIL($2), FLOOR($3), ROUND($4, 2), SQRT($5), POWER($6, $7)
            """)
            cur.execute("EXECUTE math_functions_stmt (%s, %s, %s, %s, %s, %s, %s)",
                       (-42, 4.3, 4.7, 4.567, 16, 2, 3))
            result = cur.fetchone()
            logger.info(f"数学函数结果: {result}")

            # 预编译字符串函数测试 - Extended Query
            operations.append("预编译字符串函数测试 - Extended Query")
            cur.execute("""
                PREPARE string_functions_stmt (TEXT, TEXT, TEXT, TEXT, TEXT, TEXT, TEXT, TEXT, TEXT, INTEGER) AS
                SELECT
                    LENGTH($1),
                    UPPER($2),
                    LOWER($3),
                    SUBSTRING($4 FROM 1 FOR 8),
                    CONCAT($5, ' ', $6),
                    REPLACE($7, 'World', $8),
                    TRIM($9),
                    LEFT($1, $10),
                    RIGHT($1, 3)
            """)
            cur.execute("EXECUTE string_functions_stmt (%s, %s, %s, %s, %s, %s, %s, %s, %s, %s)",
                       ('PostgreSQL', 'postgresql', 'POSTGRESQL', 'PostgreSQL', 'Hello', 'World',
                        'Hello World', 'PostgreSQL', '  spaces  ', 4))
            result = cur.fetchone()
            logger.info(f"字符串函数结果: {result}")

            # 预编译日期时间函数测试 - Extended Query
            operations.append("预编译日期时间函数测试 - Extended Query")
            cur.execute("""
                PREPARE datetime_functions_stmt AS
                SELECT
                    NOW(),
                    CURRENT_DATE,
                    CURRENT_TIME,
                    EXTRACT(YEAR FROM NOW()),
                    EXTRACT(MONTH FROM NOW()),
                    EXTRACT(DAY FROM NOW()),
                    DATE_TRUNC('month', NOW()),
                    NOW() + INTERVAL '1 day',
                    AGE(NOW(), '1990-01-01'::date)
            """)
            cur.execute("EXECUTE datetime_functions_stmt")
            result = cur.fetchone()
            logger.info(f"日期时间函数结果: {result}")

            # 创建聚合测试表 - Extended Query
            cur.execute("""
                PREPARE create_aggregates_table_stmt AS
                CREATE TABLE test_aggregates (
                    category VARCHAR(20),
                    value INTEGER
                )
            """)
            cur.execute("EXECUTE create_aggregates_table_stmt")

            # 预编译批量插入聚合测试数据 - Extended Query
            cur.execute("""
                PREPARE insert_aggregate_stmt (VARCHAR(20), INTEGER) AS
                INSERT INTO test_aggregates VALUES ($1, $2)
            """)

            test_data = [
                ('A', 10), ('A', 20), ('A', 30),
                ('B', 15), ('B', 25), ('B', 35),
                ('C', 5), ('C', 15), ('C', 25)
            ]

            for category, value in test_data:
                cur.execute("EXECUTE insert_aggregate_stmt (%s, %s)", (category, value))

            # 预编译聚合函数查询 - Extended Query
            operations.append("预编译聚合函数查询 - Extended Query")
            cur.execute("""
                PREPARE aggregate_query_stmt AS
                SELECT
                    category,
                    COUNT(*) as count,
                    SUM(value) as sum,
                    AVG(value) as avg,
                    MIN(value) as min,
                    MAX(value) as max,
                    STDDEV(value) as stddev,
                    VARIANCE(value) as variance,
                    STRING_AGG(value::text, ', ' ORDER BY value) as values_list
                FROM test_aggregates
                GROUP BY category
                ORDER BY category
            """)
            cur.execute("EXECUTE aggregate_query_stmt")
            results = cur.fetchall()
            logger.info(f"聚合函数查询返回 {len(results)} 行")

            conn.commit()

            # 清理预编译语句和表 - Extended Query
            try:
                cur.execute("DEALLOCATE math_functions_stmt")
                cur.execute("DEALLOCATE string_functions_stmt")
                cur.execute("DEALLOCATE datetime_functions_stmt")
                cur.execute("DEALLOCATE create_aggregates_table_stmt")
                cur.execute("DEALLOCATE insert_aggregate_stmt")
                cur.execute("DEALLOCATE aggregate_query_stmt")

                cur.execute("PREPARE drop_aggregates_stmt AS DROP TABLE IF EXISTS test_aggregates")
                cur.execute("EXECUTE drop_aggregates_stmt")
                cur.execute("DEALLOCATE drop_aggregates_stmt")

                conn.commit()
                operations.append("清理函数测试对象 - Extended Query")
            except:
                pass

        except Exception as e:
            logger.error(f"函数和操作符测试失败: {e}")
            conn.rollback()
            raise e
        finally:
            # 关键：先关闭数据库连接，再停止抓包
            cur.close()
            conn.close()
            logger.info("数据库连接已关闭")

            time.sleep(3)  # 等待网络包传输完成
            self.stop_capture()

        return operations

    def test_transaction_control(self):
        """测试事务控制 - Extended Query协议"""
        conn = psycopg2.connect(**DB_CONFIG)
        cur = conn.cursor()
        operations = []

        try:
            # 创建测试表 - Extended Query
            operations.append("创建事务测试表 - Extended Query")
            cur.execute("""
                PREPARE create_transaction_table_stmt AS
                CREATE TABLE transaction_test (
                    id SERIAL PRIMARY KEY,
                    name VARCHAR(100),
                    balance DECIMAL(10,2)
                )
            """)
            cur.execute("EXECUTE create_transaction_table_stmt")
            cur.execute("DEALLOCATE create_transaction_table_stmt")

            # 预编译插入初始数据 - Extended Query
            cur.execute("""
                PREPARE insert_account_stmt (VARCHAR(100), DECIMAL(10,2)) AS
                INSERT INTO transaction_test (name, balance) VALUES ($1, $2)
            """)

            cur.execute("EXECUTE insert_account_stmt (%s, %s)", ('Account A', 1000.00))
            cur.execute("EXECUTE insert_account_stmt (%s, %s)", ('Account B', 500.00))
            conn.commit()

            # 预编译更新语句 - Extended Query
            cur.execute("""
                PREPARE update_balance_stmt (DECIMAL(10,2), VARCHAR(100)) AS
                UPDATE transaction_test SET balance = balance + $1 WHERE name = $2
            """)

            # 测试事务回滚 - Extended Query
            operations.append("测试事务回滚 - Extended Query")
            self.safe_set_manual_commit(conn)
            cur.execute("BEGIN")

            cur.execute("EXECUTE update_balance_stmt (%s, %s)", (-100, 'Account A'))
            cur.execute("EXECUTE update_balance_stmt (%s, %s)", (100, 'Account B'))

            # 模拟错误，回滚事务
            cur.execute("ROLLBACK")
            operations.append("事务回滚完成 - Extended Query")

            # 测试保存点 - Extended Query
            operations.append("测试保存点 - Extended Query")
            cur.execute("BEGIN")

            cur.execute("SAVEPOINT sp1")
            cur.execute("EXECUTE update_balance_stmt (%s, %s)", (-50, 'Account A'))

            cur.execute("SAVEPOINT sp2")
            cur.execute("EXECUTE update_balance_stmt (%s, %s)", (50, 'Account B'))

            # 回滚到保存点
            cur.execute("ROLLBACK TO SAVEPOINT sp1")
            cur.execute("COMMIT")
            operations.append("保存点操作完成 - Extended Query")

            # 预编译查询最终结果 - Extended Query
            cur.execute("PREPARE select_final_stmt AS SELECT * FROM transaction_test")
            cur.execute("EXECUTE select_final_stmt")
            results = cur.fetchall()
            logger.info(f"事务测试结果: {results}")

            # 清理预编译语句和表 - Extended Query
            try:
                cur.execute("DEALLOCATE insert_account_stmt")
                cur.execute("DEALLOCATE update_balance_stmt")
                cur.execute("DEALLOCATE select_final_stmt")

                cur.execute("PREPARE drop_transaction_table_stmt AS DROP TABLE IF EXISTS transaction_test")
                cur.execute("EXECUTE drop_transaction_table_stmt")
                cur.execute("DEALLOCATE drop_transaction_table_stmt")

                conn.commit()
                operations.append("清理事务测试对象 - Extended Query")
            except:
                pass

        except Exception as e:
            logger.error(f"事务控制测试失败: {e}")
            conn.rollback()
            raise e
        finally:
            # 关键：先关闭数据库连接，再停止抓包
            cur.close()
            conn.close()
            logger.info("数据库连接已关闭")

            time.sleep(3)  # 等待网络包传输完成
            self.stop_capture()

        return operations

    def generate_comprehensive_report(self):
        """生成全面的测试报告"""
        report_content = f"""# PostgreSQL 全面流量抓包测试报告 (Extended Query协议)

## 测试概要
- 测试时间: {datetime.now().strftime('%Y-%m-%d %H:%M:%S')}
- 协议类型: **Extended Query Protocol (Parse/Bind/Execute)**
- 测试用例总数: {len(self.test_results)}
- 成功测试: {len([r for r in self.test_results if r['status'] == 'SUCCESS'])}
- 失败测试: {len([r for r in self.test_results if r['status'] == 'FAILED'])}

## Extended Query协议优势

### 1. 协议对比
- **Simple Query**: 直接发送SQL文本，服务器解析执行
- **Extended Query**: Parse(解析) -> Bind(绑定参数) -> Execute(执行)

### 2. 主要优势
- ✅ **参数化查询**: 防止SQL注入，支持类型安全
- ✅ **服务器端缓存**: 预编译语句重用，提高性能
- ✅ **二进制传输**: 支持二进制数据格式，减少转换开销
- ✅ **精细错误处理**: 更详细的错误信息和状态

### 3. 网络流量特征
- **Parse消息**: 包含SQL模板和参数占位符
- **Bind消息**: 绑定具体参数值
- **Execute消息**: 执行已绑定的语句
- **Describe消息**: 获取语句描述信息

## 测试结果详情

"""

        for result in self.test_results:
            report_content += f"""### {result['test_name']}
- 状态: {result['status']}
- 时间: {result['timestamp']}
- PCAP文件: {os.path.basename(result['pcap_file'])}
- 操作数量: {len(result['operations'])}

"""
            if result['status'] == 'FAILED':
                report_content += f"- 错误信息: {result.get('error', 'Unknown error')}\n\n"
            else:
                report_content += f"- 执行的操作:\n"
                for i, op in enumerate(result['operations'][:5], 1):  # 只显示前5个操作
                    report_content += f"  {i}. {op[:100]}{'...' if len(op) > 100 else ''}\n"
                if len(result['operations']) > 5:
                    report_content += f"  ... 还有 {len(result['operations']) - 5} 个操作\n"
                report_content += "\n"

        report_content += """
## Extended Query协议分析要点

### 1. Wireshark过滤器
```
# 过滤Extended Query消息
pgsql.type == "Parse" or pgsql.type == "Bind" or pgsql.type == "Execute"

# 查看预编译语句
pgsql.query contains "PREPARE"

# 分析参数绑定
pgsql.type == "Bind"

# 观察语句执行
pgsql.type == "Execute"
```

### 2. 性能分析
- **Parse频率**: 观察语句解析的频率
- **Bind/Execute比例**: 分析语句重用情况
- **参数传输**: 检查参数数据大小和类型
- **错误处理**: 分析ErrorResponse消息

### 3. 安全分析
- **参数化查询**: 确认所有用户输入都通过参数传递
- **SQL注入防护**: 验证没有直接拼接SQL字符串
- **类型安全**: 检查参数类型匹配

## PostgreSQL功能覆盖

### 1. DDL操作
- CREATE/DROP SCHEMA, TABLE, INDEX, VIEW
- ALTER TABLE操作
- 自定义类型和域

### 2. 数据类型
- 数值类型: INTEGER, DECIMAL, REAL等
- 字符类型: CHAR, VARCHAR, TEXT
- 日期时间类型: DATE, TIME, TIMESTAMP
- 特殊类型: JSON, JSONB, UUID, ARRAY
- 几何类型: POINT, LINE, POLYGON等
- 网络类型: INET, CIDR, MACADDR

### 3. DML操作
- INSERT, UPDATE, DELETE
- 复杂SELECT查询
- 批量操作

### 4. 高级查询
- 窗口函数
- CTE (Common Table Expressions)
- 递归查询
- 子查询

### 5. 函数和操作符
- 数学函数
- 字符串函数
- 日期时间函数
- 聚合函数

### 6. 事务控制
- BEGIN/COMMIT/ROLLBACK
- 保存点操作
- 事务隔离级别

## 抓包文件说明
所有PCAP文件保存在: {CAPTURE_CONFIG['local_dir']}

使用Wireshark分析时重点关注:
1. **协议消息序列**: Parse -> Bind -> Execute
2. **参数传输**: 观察参数的二进制编码
3. **语句缓存**: PREPARE/DEALLOCATE的使用模式
4. **错误处理**: ErrorResponse消息的详细信息
5. **性能优化**: 语句重用和批量操作的效率

## 建议和最佳实践

### 1. 应用开发
- 使用参数化查询防止SQL注入
- 重用PreparedStatement提高性能
- 合理使用批量操作
- 正确处理事务边界

### 2. 性能优化
- 监控Parse/Execute比例
- 优化频繁执行的语句
- 使用连接池减少连接开销
- 合理设置语句缓存大小

### 3. 安全考虑
- 所有用户输入都应参数化
- 定期审查SQL语句模板
- 监控异常的查询模式
- 实施最小权限原则
"""

        # 保存报告
        report_file = os.path.join(CAPTURE_CONFIG['local_dir'],
                                 f"postgres_extended_test_report_{datetime.now().strftime('%Y%m%d_%H%M%S')}.md")
        with open(report_file, 'w', encoding='utf-8') as f:
            f.write(report_content)

        logger.info(f"测试报告已生成: {report_file}")
        return report_file

    def cleanup(self):
        """清理资源"""
        if self.sftp:
            self.sftp.close()
        if self.ssh:
            self.ssh.close()

def main():
    """主函数"""
    capture = ComprehensiveTrafficCaptureExtended()

    try:
        # 定义所有测试用例 - Extended Query协议版本
        test_cases = [
            ("DDL操作", capture.test_ddl_operations),
            ("数据类型", capture.test_data_types),
            ("DML操作", capture.test_dml_operations),
            ("复杂查询", capture.test_complex_queries),
            ("函数和操作符", capture.test_functions_and_operators),
            ("事务控制", capture.test_transaction_control)
        ]

        logger.info(f"开始执行 {len(test_cases)} 个PostgreSQL Extended Query测试用例")
        logger.info("=" * 80)
        logger.info("PostgreSQL 全面流量抓包测试 (Extended Query协议)")
        logger.info("使用Parse/Bind/Execute消息序列替代Simple Query")
        logger.info("涵盖DDL、DML、复杂查询、函数、事务等所有主要功能")
        logger.info("=" * 80)

        # 执行所有测试
        for test_name, test_func in test_cases:
            try:
                logger.info(f"\n准备执行测试: {test_name}")
                capture.execute_test_with_capture(test_name, test_func)
                time.sleep(2)  # 测试间隔
            except Exception as e:
                logger.error(f"测试 {test_name} 执行失败: {e}")
                continue

        # 生成报告
        report_file = capture.generate_comprehensive_report()

        logger.info("\n" + "=" * 80)
        logger.info("所有PostgreSQL Extended Query测试完成！")
        logger.info(f"测试报告: {report_file}")
        logger.info(f"PCAP文件保存在: {CAPTURE_CONFIG['local_dir']}")
        logger.info("=" * 80)

        # 打印测试结果摘要
        success_count = len([r for r in capture.test_results if r['status'] == 'SUCCESS'])
        failed_count = len([r for r in capture.test_results if r['status'] == 'FAILED'])

        logger.info(f"\n测试结果摘要:")
        logger.info(f"- 总测试数: {len(capture.test_results)}")
        logger.info(f"- 成功: {success_count}")
        logger.info(f"- 失败: {failed_count}")
        logger.info(f"- 成功率: {success_count/len(capture.test_results)*100:.1f}%")

    except KeyboardInterrupt:
        logger.info("\n测试被用户中断")
    except Exception as e:
        logger.error(f"测试执行过程中发生错误: {e}")
    finally:
        capture.cleanup()

if __name__ == "__main__":
    main()
