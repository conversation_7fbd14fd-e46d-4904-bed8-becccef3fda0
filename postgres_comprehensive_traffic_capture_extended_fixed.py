#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
PostgreSQL全面流量抓包脚本 (Extended Query协议修复版本)
专注于能正常工作的Extended Query功能
避免DDL语句的PREPARE问题，重点展示DML和查询的Extended Query特性
"""
import psycopg2
import psycopg2.extensions
import paramiko
import time
import os
from datetime import datetime, date, timedelta
import sys
from pathlib import Path
import subprocess
import logging
import json
import uuid
import decimal
from typing import List, Dict, Any

# 配置日志
logging.basicConfig(
    level=logging.INFO,
    format='%(asctime)s - %(levelname)s - %(message)s'
)
logger = logging.getLogger(__name__)

# 服务器配置
SERVER_CONFIG = {
    'hostname': '**************',
    'username': 'root',
    'password': 'root@123',
    'port': 22
}

# 数据库配置
DB_CONFIG = {
    'host': '**************',
    'port': 5432,
    'database': 'postgres',
    'user': 'postgres',
    'password': 'mysecretpassword'
}

# 抓包配置
CAPTURE_CONFIG = {
    'interface': 'eth0',
    'port': 5432,
    'remote_dir': '/root/pcap',
    'local_dir': './postgres_comprehensive_extended_pcap'
}

class ComprehensiveTrafficCaptureExtended:
    def __init__(self):
        self.ssh = None
        self.sftp = None
        self.connect_ssh()
        self.setup_directories()
        self.test_results = []
        
    def connect_ssh(self):
        """连接SSH服务器"""
        try:
            self.ssh = paramiko.SSHClient()
            self.ssh.set_missing_host_key_policy(paramiko.AutoAddPolicy())
            self.ssh.connect(**SERVER_CONFIG)
            self.sftp = self.ssh.open_sftp()
            logger.info("SSH连接成功")
        except Exception as e:
            logger.error(f"SSH连接失败: {e}")
            sys.exit(1)

    def setup_directories(self):
        """设置远程和本地目录"""
        self.ssh.exec_command(f"mkdir -p {CAPTURE_CONFIG['remote_dir']}")
        Path(CAPTURE_CONFIG['local_dir']).mkdir(parents=True, exist_ok=True)

    def start_capture(self, capture_type):
        """开始抓包"""
        timestamp = datetime.now().strftime('%Y%m%d_%H%M%S')
        remote_file = f"{CAPTURE_CONFIG['remote_dir']}/pg_ext_{capture_type}_{timestamp}.pcap"
        cmd = f"tcpdump -i {CAPTURE_CONFIG['interface']} -s 0 -w {remote_file} port {CAPTURE_CONFIG['port']}"
        logger.info(f"开始抓包: {capture_type} (Extended Query协议)")
        self.ssh.exec_command(f"nohup {cmd} > /dev/null 2>&1 &")
        time.sleep(2)
        return remote_file

    def stop_capture(self):
        """停止抓包"""
        logger.info("停止抓包...")
        self.ssh.exec_command("pkill tcpdump")
        time.sleep(2)

    def download_pcap(self, remote_file):
        """下载pcap文件到本地"""
        filename = os.path.basename(remote_file)
        local_file = os.path.join(CAPTURE_CONFIG['local_dir'], filename)
        self.sftp.get(remote_file, local_file)
        logger.info(f"已下载: {filename}")
        return local_file

    def execute_test_with_capture(self, test_name: str, test_func):
        """执行测试并抓包"""
        logger.info(f"\n=== 开始 {test_name} 测试 (Extended Query) ===")
        remote_file = self.start_capture(test_name.lower().replace(' ', '_'))
        
        try:
            operations = test_func()
            local_file = self.download_pcap(remote_file)
            
            self.test_results.append({
                'test_name': test_name,
                'pcap_file': local_file,
                'operations': operations,
                'status': 'SUCCESS',
                'timestamp': datetime.now().isoformat()
            })
            logger.info(f"{test_name} 测试完成")
            
        except Exception as e:
            logger.error(f"{test_name} 测试失败: {e}")
            self.stop_capture()
            try:
                local_file = self.download_pcap(remote_file)
                self.test_results.append({
                    'test_name': test_name,
                    'pcap_file': local_file,
                    'operations': [],
                    'status': 'FAILED',
                    'error': str(e),
                    'timestamp': datetime.now().isoformat()
                })
            except:
                pass

    def test_extended_query_dml(self):
        """测试Extended Query DML操作"""
        conn = psycopg2.connect(**DB_CONFIG)
        cur = conn.cursor()
        operations = []
        
        try:
            # 创建测试表（DDL直接执行）
            operations.append("创建测试表")
            cur.execute("""
                CREATE TABLE IF NOT EXISTS extended_query_test (
                    id SERIAL PRIMARY KEY,
                    name VARCHAR(100),
                    age INTEGER,
                    salary DECIMAL(10,2),
                    department VARCHAR(50),
                    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP
                )
            """)
            
            # 清空表
            cur.execute("TRUNCATE TABLE extended_query_test RESTART IDENTITY")
            conn.commit()
            
            # 预编译INSERT语句 - Extended Query核心功能
            operations.append("预编译INSERT语句 - Extended Query")
            cur.execute("""
                PREPARE insert_employee_stmt (VARCHAR(100), INTEGER, DECIMAL(10,2), VARCHAR(50)) AS
                INSERT INTO extended_query_test (name, age, salary, department) 
                VALUES ($1, $2, $3, $4) RETURNING id
            """)
            
            # 批量插入数据 - Extended Query
            operations.append("批量插入数据 - Extended Query")
            employees = [
                ('Alice Johnson', 30, 75000.00, 'Engineering'),
                ('Bob Smith', 25, 65000.00, 'Marketing'),
                ('Carol Davis', 35, 85000.00, 'Engineering'),
                ('David Wilson', 28, 70000.00, 'Sales'),
                ('Eve Brown', 32, 80000.00, 'HR')
            ]
            
            inserted_ids = []
            for name, age, salary, dept in employees:
                cur.execute("EXECUTE insert_employee_stmt (%s, %s, %s, %s)", 
                           (name, age, salary, dept))
                result = cur.fetchone()
                inserted_ids.append(result[0])
                logger.info(f"插入员工 {name}，ID: {result[0]}")
            
            # 预编译UPDATE语句 - Extended Query
            operations.append("预编译UPDATE语句 - Extended Query")
            cur.execute("""
                PREPARE update_salary_stmt (DECIMAL(3,2), VARCHAR(50)) AS
                UPDATE extended_query_test SET salary = salary * $1 
                WHERE department = $2 RETURNING name, salary
            """)
            
            cur.execute("EXECUTE update_salary_stmt (%s, %s)", (1.1, 'Engineering'))
            updated_employees = cur.fetchall()
            logger.info(f"更新了 {len(updated_employees)} 名工程部门员工薪资")
            
            # 预编译复杂SELECT语句 - Extended Query
            operations.append("预编译复杂SELECT语句 - Extended Query")
            cur.execute("""
                PREPARE complex_select_stmt (INTEGER, DECIMAL(10,2)) AS
                SELECT 
                    department,
                    COUNT(*) as emp_count,
                    AVG(salary) as avg_salary,
                    MAX(salary) as max_salary,
                    MIN(age) as min_age
                FROM extended_query_test 
                WHERE age > $1 AND salary > $2
                GROUP BY department 
                HAVING COUNT(*) > 0
                ORDER BY avg_salary DESC
            """)
            
            cur.execute("EXECUTE complex_select_stmt (%s, %s)", (25, 60000))
            dept_stats = cur.fetchall()
            logger.info(f"部门统计查询返回 {len(dept_stats)} 个部门")
            
            # 预编译条件DELETE语句 - Extended Query
            operations.append("预编译DELETE语句 - Extended Query")
            cur.execute("""
                PREPARE delete_by_age_stmt (INTEGER) AS
                DELETE FROM extended_query_test WHERE age < $1 RETURNING name, age
            """)
            
            cur.execute("EXECUTE delete_by_age_stmt (%s)", (27,))
            deleted_employees = cur.fetchall()
            logger.info(f"删除了 {len(deleted_employees)} 名年轻员工")
            
            conn.commit()

            # 清理预编译语句
            operations.append("清理预编译语句")
            cur.execute("DEALLOCATE insert_employee_stmt")
            cur.execute("DEALLOCATE update_salary_stmt")
            cur.execute("DEALLOCATE complex_select_stmt")
            cur.execute("DEALLOCATE delete_by_age_stmt")
            
            # 清理表
            cur.execute("DROP TABLE IF EXISTS extended_query_test")
            conn.commit()
            operations.append("清理测试表")

        except Exception as e:
            logger.error(f"Extended Query DML测试失败: {e}")
            conn.rollback()
            raise e
        finally:
            cur.close()
            conn.close()
            time.sleep(3)
            self.stop_capture()

        return operations

    def test_extended_query_functions(self):
        """测试Extended Query函数和表达式"""
        conn = psycopg2.connect(**DB_CONFIG)
        cur = conn.cursor()
        operations = []

        try:
            # 预编译数学函数测试 - Extended Query
            operations.append("预编译数学函数测试 - Extended Query")
            cur.execute("""
                PREPARE math_functions_stmt (INTEGER, DECIMAL(5,3), DECIMAL(5,3), DECIMAL(5,3), INTEGER, INTEGER, INTEGER) AS
                SELECT
                    ABS($1) as abs_value,
                    CEIL($2) as ceil_value,
                    FLOOR($3) as floor_value,
                    ROUND($4, 2) as round_value,
                    SQRT($5) as sqrt_value,
                    POWER($6, $7) as power_value,
                    $1 + $5 as sum_value,
                    $2 * $3 as multiply_value
            """)

            cur.execute("EXECUTE math_functions_stmt (%s, %s, %s, %s, %s, %s, %s)",
                       (-42, 4.3, 4.7, 4.567, 16, 2, 3))
            result = cur.fetchone()
            logger.info(f"数学函数结果: {result}")

            # 预编译字符串函数测试 - Extended Query
            operations.append("预编译字符串函数测试 - Extended Query")
            cur.execute("""
                PREPARE string_functions_stmt (TEXT, TEXT, TEXT, TEXT, TEXT, TEXT, TEXT, TEXT, TEXT, INTEGER) AS
                SELECT
                    LENGTH($1) as length_val,
                    UPPER($2) as upper_val,
                    LOWER($3) as lower_val,
                    SUBSTRING($4 FROM 1 FOR 8) as substring_val,
                    CONCAT($5, ' ', $6) as concat_val,
                    REPLACE($7, 'World', $8) as replace_val,
                    TRIM($9) as trim_val,
                    LEFT($1, $10) as left_val,
                    RIGHT($1, 3) as right_val,
                    POSITION('SQL' IN $1) as position_val
            """)

            cur.execute("EXECUTE string_functions_stmt (%s, %s, %s, %s, %s, %s, %s, %s, %s, %s)",
                       ('PostgreSQL', 'postgresql', 'POSTGRESQL', 'PostgreSQL', 'Hello', 'World',
                        'Hello World', 'PostgreSQL', '  spaces  ', 4))
            result = cur.fetchone()
            logger.info(f"字符串函数结果: {result}")

            # 预编译日期时间函数测试 - Extended Query
            operations.append("预编译日期时间函数测试 - Extended Query")
            cur.execute("""
                PREPARE datetime_functions_stmt (DATE, TIMESTAMP, INTERVAL) AS
                SELECT
                    $1 as input_date,
                    $2 as input_timestamp,
                    $3 as input_interval,
                    EXTRACT(YEAR FROM $1) as year_val,
                    EXTRACT(MONTH FROM $1) as month_val,
                    EXTRACT(DAY FROM $1) as day_val,
                    DATE_TRUNC('month', $2) as trunc_month,
                    $2 + $3 as date_add,
                    AGE($2, $1) as age_diff,
                    NOW() as current_time
            """)

            cur.execute("EXECUTE datetime_functions_stmt (%s, %s, %s)",
                       ('2023-12-25', '2023-12-25 14:30:00', '1 month 15 days'))
            result = cur.fetchone()
            logger.info(f"日期时间函数结果: {result}")

            # 预编译条件表达式测试 - Extended Query
            operations.append("预编译条件表达式测试 - Extended Query")
            cur.execute("""
                PREPARE conditional_stmt (INTEGER, TEXT, DECIMAL(10,2), BOOLEAN) AS
                SELECT
                    CASE
                        WHEN $1 > 100 THEN 'High'
                        WHEN $1 > 50 THEN 'Medium'
                        ELSE 'Low'
                    END as category,
                    COALESCE($2, 'Unknown') as name_or_default,
                    GREATEST($3, 0) as positive_amount,
                    LEAST($3, 1000000) as capped_amount,
                    NULLIF($2, '') as non_empty_text,
                    $4 AND $1 > 0 as combined_condition
            """)

            cur.execute("EXECUTE conditional_stmt (%s, %s, %s, %s)",
                       (75, 'Test Value', 12345.67, True))
            result = cur.fetchone()
            logger.info(f"条件表达式结果: {result}")

            # 清理预编译语句
            operations.append("清理函数测试预编译语句")
            cur.execute("DEALLOCATE math_functions_stmt")
            cur.execute("DEALLOCATE string_functions_stmt")
            cur.execute("DEALLOCATE datetime_functions_stmt")
            cur.execute("DEALLOCATE conditional_stmt")

        except Exception as e:
            logger.error(f"Extended Query函数测试失败: {e}")
            raise e
        finally:
            cur.close()
            conn.close()
            time.sleep(3)
            self.stop_capture()

        return operations

    def test_extended_query_transactions(self):
        """测试Extended Query事务控制"""
        conn = psycopg2.connect(**DB_CONFIG)
        cur = conn.cursor()
        operations = []

        try:
            # 创建测试表
            operations.append("创建事务测试表")
            cur.execute("""
                CREATE TABLE IF NOT EXISTS transaction_test (
                    id SERIAL PRIMARY KEY,
                    account_name VARCHAR(100),
                    balance DECIMAL(10,2),
                    last_updated TIMESTAMP DEFAULT CURRENT_TIMESTAMP
                )
            """)

            cur.execute("TRUNCATE TABLE transaction_test RESTART IDENTITY")
            conn.commit()

            # 预编译事务相关语句 - Extended Query
            operations.append("预编译事务操作语句 - Extended Query")
            cur.execute("""
                PREPARE insert_account_stmt (VARCHAR(100), DECIMAL(10,2)) AS
                INSERT INTO transaction_test (account_name, balance) VALUES ($1, $2) RETURNING id
            """)

            cur.execute("""
                PREPARE update_balance_stmt (DECIMAL(10,2), VARCHAR(100)) AS
                UPDATE transaction_test SET balance = balance + $1, last_updated = CURRENT_TIMESTAMP
                WHERE account_name = $2 RETURNING balance
            """)

            cur.execute("""
                PREPARE get_balance_stmt (VARCHAR(100)) AS
                SELECT account_name, balance FROM transaction_test WHERE account_name = $1
            """)

            # 插入初始账户数据
            operations.append("插入初始账户数据 - Extended Query")
            cur.execute("EXECUTE insert_account_stmt (%s, %s)", ('Account A', 1000.00))
            cur.execute("EXECUTE insert_account_stmt (%s, %s)", ('Account B', 500.00))
            conn.commit()

            # 测试事务回滚 - Extended Query
            operations.append("测试事务回滚 - Extended Query")
            conn.autocommit = False
            cur.execute("BEGIN")

            # 执行转账操作
            cur.execute("EXECUTE update_balance_stmt (%s, %s)", (-200, 'Account A'))
            cur.execute("EXECUTE update_balance_stmt (%s, %s)", (200, 'Account B'))

            # 检查中间状态
            cur.execute("EXECUTE get_balance_stmt (%s)", ('Account A',))
            balance_a = cur.fetchone()
            logger.info(f"事务中Account A余额: {balance_a}")

            # 模拟错误，回滚事务
            cur.execute("ROLLBACK")
            operations.append("事务回滚完成 - Extended Query")

            # 测试保存点 - Extended Query
            operations.append("测试保存点 - Extended Query")
            cur.execute("BEGIN")

            cur.execute("SAVEPOINT sp1")
            cur.execute("EXECUTE update_balance_stmt (%s, %s)", (-100, 'Account A'))

            cur.execute("SAVEPOINT sp2")
            cur.execute("EXECUTE update_balance_stmt (%s, %s)", (100, 'Account B'))

            # 回滚到第一个保存点
            cur.execute("ROLLBACK TO SAVEPOINT sp1")

            # 执行不同的操作
            cur.execute("EXECUTE update_balance_stmt (%s, %s)", (-50, 'Account A'))
            cur.execute("EXECUTE update_balance_stmt (%s, %s)", (50, 'Account B'))

            cur.execute("COMMIT")
            operations.append("保存点操作完成 - Extended Query")

            # 查询最终结果
            cur.execute("EXECUTE get_balance_stmt (%s)", ('Account A',))
            final_balance_a = cur.fetchone()
            cur.execute("EXECUTE get_balance_stmt (%s)", ('Account B',))
            final_balance_b = cur.fetchone()
            logger.info(f"最终余额 - A: {final_balance_a}, B: {final_balance_b}")

            # 清理预编译语句和表
            operations.append("清理事务测试对象")
            cur.execute("DEALLOCATE insert_account_stmt")
            cur.execute("DEALLOCATE update_balance_stmt")
            cur.execute("DEALLOCATE get_balance_stmt")

            cur.execute("DROP TABLE IF EXISTS transaction_test")
            conn.commit()

        except Exception as e:
            logger.error(f"Extended Query事务测试失败: {e}")
            try:
                conn.rollback()
            except:
                pass
            raise e
        finally:
            cur.close()
            conn.close()
            time.sleep(3)
            self.stop_capture()

        return operations

    def generate_comprehensive_report(self):
        """生成全面的测试报告"""
        report_content = f"""# PostgreSQL Extended Query协议抓包测试报告

## 测试概要
- 测试时间: {datetime.now().strftime('%Y-%m-%d %H:%M:%S')}
- 协议类型: **Extended Query Protocol (Parse/Bind/Execute)**
- 测试用例总数: {len(self.test_results)}
- 成功测试: {len([r for r in self.test_results if r['status'] == 'SUCCESS'])}
- 失败测试: {len([r for r in self.test_results if r['status'] == 'FAILED'])}

## Extended Query协议特点

### 1. 协议消息序列
```
Client -> Server: Parse (SQL模板 + 参数占位符)
Server -> Client: ParseComplete
Client -> Server: Bind (参数值绑定)
Server -> Client: BindComplete
Client -> Server: Execute
Server -> Client: DataRow + CommandComplete
```

### 2. 主要优势
- ✅ **参数化查询**: 防止SQL注入，类型安全
- ✅ **语句重用**: 一次Parse，多次Bind+Execute
- ✅ **性能优化**: 服务器端查询计划缓存
- ✅ **二进制传输**: 减少数据转换开销

## 测试结果详情

"""

        for result in self.test_results:
            report_content += f"""### {result['test_name']}
- 状态: {result['status']}
- 时间: {result['timestamp']}
- PCAP文件: {os.path.basename(result['pcap_file'])}
- 操作数量: {len(result['operations'])}

"""
            if result['status'] == 'FAILED':
                report_content += f"- 错误信息: {result.get('error', 'Unknown error')}\n\n"
            else:
                report_content += f"- 执行的操作:\n"
                for i, op in enumerate(result['operations'][:5], 1):
                    report_content += f"  {i}. {op[:80]}{'...' if len(op) > 80 else ''}\n"
                if len(result['operations']) > 5:
                    report_content += f"  ... 还有 {len(result['operations']) - 5} 个操作\n"
                report_content += "\n"

        report_content += f"""
## Wireshark分析指南

### 1. 过滤Extended Query消息
```
pgsql.type == "Parse" or pgsql.type == "Bind" or pgsql.type == "Execute"
```

### 2. 分析PREPARE语句
```
pgsql.query contains "PREPARE"
```

### 3. 观察参数绑定
```
pgsql.type == "Bind"
```

### 4. 查看语句执行
```
pgsql.query contains "EXECUTE"
```

## 抓包文件位置
所有PCAP文件保存在: {CAPTURE_CONFIG['local_dir']}

## 性能分析要点
1. **Parse频率**: 观察语句解析的频率，理想情况下每个语句只Parse一次
2. **Bind/Execute比例**: 分析语句重用情况，比例越高说明重用越好
3. **参数传输**: 检查参数数据的大小和编码方式
4. **错误处理**: 分析ErrorResponse消息的详细信息

## 安全分析要点
1. **参数化查询**: 确认所有用户输入都通过参数传递，而非字符串拼接
2. **SQL注入防护**: 验证没有直接在SQL中嵌入用户数据
3. **类型安全**: 检查参数类型是否正确匹配

这个Extended Query版本的测试提供了更真实的现代数据库应用网络行为模拟。
"""

        # 保存报告
        report_file = os.path.join(CAPTURE_CONFIG['local_dir'],
                                 f"postgres_extended_test_report_{datetime.now().strftime('%Y%m%d_%H%M%S')}.md")
        with open(report_file, 'w', encoding='utf-8') as f:
            f.write(report_content)

        logger.info(f"测试报告已生成: {report_file}")
        return report_file

    def cleanup(self):
        """清理资源"""
        if self.sftp:
            self.sftp.close()
        if self.ssh:
            self.ssh.close()

def main():
    """主函数"""
    capture = ComprehensiveTrafficCaptureExtended()

    try:
        # 定义测试用例 - 专注于Extended Query协议的核心功能
        test_cases = [
            ("Extended Query DML操作", capture.test_extended_query_dml),
            ("Extended Query函数和表达式", capture.test_extended_query_functions),
            ("Extended Query事务控制", capture.test_extended_query_transactions)
        ]

        logger.info(f"开始执行 {len(test_cases)} 个PostgreSQL Extended Query测试用例")
        logger.info("=" * 80)
        logger.info("PostgreSQL Extended Query协议抓包测试 (修复版本)")
        logger.info("专注于PREPARE/EXECUTE语句的网络行为分析")
        logger.info("涵盖DML操作、函数调用、事务控制等核心功能")
        logger.info("=" * 80)

        # 执行所有测试
        for test_name, test_func in test_cases:
            try:
                logger.info(f"\n准备执行测试: {test_name}")
                capture.execute_test_with_capture(test_name, test_func)
                time.sleep(2)  # 测试间隔
            except Exception as e:
                logger.error(f"测试 {test_name} 执行失败: {e}")
                continue

        # 生成报告
        report_file = capture.generate_comprehensive_report()

        logger.info("\n" + "=" * 80)
        logger.info("所有PostgreSQL Extended Query测试完成！")
        logger.info(f"测试报告: {report_file}")
        logger.info(f"PCAP文件保存在: {CAPTURE_CONFIG['local_dir']}")
        logger.info("=" * 80)

        # 打印测试结果摘要
        success_count = len([r for r in capture.test_results if r['status'] == 'SUCCESS'])
        failed_count = len([r for r in capture.test_results if r['status'] == 'FAILED'])

        logger.info(f"\n测试结果摘要:")
        logger.info(f"- 总测试数: {len(capture.test_results)}")
        logger.info(f"- 成功: {success_count}")
        logger.info(f"- 失败: {failed_count}")
        logger.info(f"- 成功率: {success_count/len(capture.test_results)*100:.1f}%")

    except KeyboardInterrupt:
        logger.info("\n测试被用户中断")
    except Exception as e:
        logger.error(f"测试执行过程中发生错误: {e}")
    finally:
        capture.cleanup()

if __name__ == "__main__":
    main()
