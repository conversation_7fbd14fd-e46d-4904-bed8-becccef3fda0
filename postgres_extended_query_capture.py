#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
PostgreSQL Extended Query协议流量抓包脚本
目标：使用Extended Query协议（Parse/Bind/Execute）抓取PostgreSQL各种场景的流量
包括：参数化查询、预处理语句、批量操作、二进制格式等
"""
import psycopg2
import psycopg2.extras
import paramiko
import time
import os
from datetime import datetime
import sys
from pathlib import Path
import subprocess
import logging
import threading
import random
import string

# 配置日志
logging.basicConfig(
    level=logging.INFO,
    format='%(asctime)s - %(levelname)s - %(message)s'
)
logger = logging.getLogger(__name__)

# 服务器配置
SERVER_CONFIG = {
    'hostname': '**************',
    'username': 'root',
    'password': 'root@123',
    'port': 22
}

# 数据库配置
DB_CONFIG = {
    'host': '**************',
    'port': 5432,
    'database': 'postgres',
    'user': 'postgres',
    'password': 'mysecretpassword'
}

# 抓包配置
CAPTURE_CONFIG = {
    'interface': 'eth0',
    'port': 5432,
    'remote_dir': '/root/pcap_extended',
    'local_dir': './postgres_extended_pcap'
}

class CaptureReport:
    def __init__(self, local_dir):
        self.local_dir = local_dir
        self.captures = []
        self.start_time = datetime.now()
    
    def add_capture(self, capture_type, file_path, operation_details):
        """添加抓包记录"""
        self.captures.append({
            'type': capture_type,
            'file': file_path,
            'details': operation_details,
            'timestamp': datetime.now().strftime('%Y-%m-%d %H:%M:%S'),
            'file_size': os.path.getsize(file_path) if os.path.exists(file_path) else 0
        })
    
    def get_packet_stats(self, pcap_file):
        """获取pcap文件统计信息"""
        try:
            cmd = f"tcpdump -r {pcap_file} -n 2>&1 | wc -l"
            result = subprocess.run(cmd, shell=True, capture_output=True, text=True)
            packet_count = int(result.stdout.strip())
            return packet_count
        except Exception as e:
            logger.error(f"获取包统计信息失败: {e}")
            return 0
    
    def generate_report(self):
        """生成Markdown格式报告"""
        end_time = datetime.now()
        duration = end_time - self.start_time
        
        report = f"""# PostgreSQL Extended Query协议流量抓包报告

## 执行概要
- 开始时间：{self.start_time.strftime('%Y-%m-%d %H:%M:%S')}
- 结束时间：{end_time.strftime('%Y-%m-%d %H:%M:%S')}
- 总运行时长：{duration.total_seconds():.2f} 秒
- 协议类型：Extended Query (Parse/Bind/Execute)

## 抓包统计
| 类型 | 文件名 | 时间 | 文件大小 | 包数量 |
|------|--------|------|----------|--------|
"""
        
        for capture in self.captures:
            packet_count = self.get_packet_stats(capture['file'])
            file_size_mb = capture['file_size'] / (1024 * 1024)
            report += f"| {capture['type']} | {os.path.basename(capture['file'])} | {capture['timestamp']} | {file_size_mb:.2f}MB | {packet_count} |\n"
        
        report += "\n## 详细操作记录\n"
        
        for capture in self.captures:
            report += f"""
### {capture['type']} 测试
- 时间：{capture['timestamp']}
- 文件：{os.path.basename(capture['file'])}
- 操作详情：
```
{capture['details']}
```
"""
        
        report_file = os.path.join(self.local_dir, f"extended_query_report_{datetime.now().strftime('%Y%m%d_%H%M%S')}.md")
        with open(report_file, 'w', encoding='utf-8') as f:
            f.write(report)
        
        return report_file

class ExtendedQueryCapture:
    def __init__(self):
        self.ssh = None
        self.sftp = None
        self.connect_ssh()
        self.setup_directories()
        self.report = CaptureReport(CAPTURE_CONFIG['local_dir'])
        
    def connect_ssh(self):
        """连接SSH服务器"""
        try:
            self.ssh = paramiko.SSHClient()
            self.ssh.set_missing_host_key_policy(paramiko.AutoAddPolicy())
            self.ssh.connect(**SERVER_CONFIG)
            self.sftp = self.ssh.open_sftp()
            logger.info("SSH连接成功")
        except Exception as e:
            logger.error(f"SSH连接失败: {e}")
            sys.exit(1)

    def setup_directories(self):
        """设置远程和本地目录"""
        self.ssh.exec_command(f"mkdir -p {CAPTURE_CONFIG['remote_dir']}")
        Path(CAPTURE_CONFIG['local_dir']).mkdir(parents=True, exist_ok=True)

    def start_capture(self, capture_type):
        """开始抓包"""
        timestamp = datetime.now().strftime('%Y%m%d_%H%M%S')
        remote_file = f"{CAPTURE_CONFIG['remote_dir']}/pg_extended_{capture_type}_{timestamp}.pcap"
        cmd = f"tcpdump -i {CAPTURE_CONFIG['interface']} -s 0 -w {remote_file} port {CAPTURE_CONFIG['port']}"
        logger.info(f"执行抓包命令: {cmd}")
        self.ssh.exec_command(f"nohup {cmd} > /dev/null 2>&1 &")
        time.sleep(2)
        return remote_file

    def stop_capture(self):
        """停止抓包"""
        logger.info("停止抓包...")
        self.ssh.exec_command("pkill tcpdump")
        time.sleep(2)

    def download_pcap(self, remote_file):
        """下载pcap文件到本地"""
        filename = os.path.basename(remote_file)
        local_file = os.path.join(CAPTURE_CONFIG['local_dir'], filename)
        self.sftp.get(remote_file, local_file)
        logger.info(f"已下载: {filename}")
        return local_file

    def execute_parameterized_query_test(self):
        """执行参数化查询测试 - Extended Query协议"""
        logger.info("\n开始参数化查询测试（Extended Query）...")
        remote_file = self.start_capture('parameterized_query')
        
        operations = []
        
        try:
            conn = psycopg2.connect(**DB_CONFIG)
            cur = conn.cursor()
            
            # 创建测试表
            cur.execute("""
                CREATE TABLE IF NOT EXISTS param_test (
                    id SERIAL PRIMARY KEY,
                    name VARCHAR(50),
                    age INTEGER,
                    score DECIMAL(5,2),
                    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP
                )
            """)
            operations.append("CREATE TABLE param_test")
            
            # 参数化插入 - 这会使用Extended Query协议
            users = [
                ('张三', 25, 89.5),
                ('李四', 30, 92.3),
                ('王五', 28, 87.8),
                ('赵六', 35, 95.1),
                ('孙七', 22, 83.7)
            ]
            
            for name, age, score in users:
                cur.execute("""
                    INSERT INTO param_test (name, age, score) 
                    VALUES (%s, %s, %s)
                """, (name, age, score))
                operations.append(f"INSERT with parameters: {name}, {age}, {score}")
            
            conn.commit()
            
            # 参数化查询
            cur.execute("SELECT * FROM param_test WHERE age > %s", (25,))
            result = cur.fetchall()
            logger.info(f"查询结果（年龄>25）: {len(result)}条记录")
            operations.append("SELECT with age parameter > 25")
            
            # 参数化更新
            cur.execute("""
                UPDATE param_test 
                SET score = score + %s 
                WHERE name = %s
            """, (5.0, '张三'))
            operations.append("UPDATE with parameters: score +5.0, name='张三'")
            
            # 参数化删除
            cur.execute("DELETE FROM param_test WHERE score < %s", (85.0,))
            operations.append("DELETE with score parameter < 85.0")
            
            conn.commit()
            
            time.sleep(5)
            self.stop_capture()
            local_file = self.download_pcap(remote_file)
            
            self.report.add_capture('参数化查询测试', local_file, '\n'.join(operations))
            
            # 清理
            cur.execute("DROP TABLE param_test")
            conn.commit()
            
        except Exception as e:
            logger.error(f"参数化查询测试出错: {e}")
            raise e
        finally:
            if 'cur' in locals():
                cur.close()
            if 'conn' in locals():
                conn.close()

    def execute_prepared_statement_test(self):
        """执行预处理语句测试 - Extended Query协议"""
        logger.info("\n开始预处理语句测试（Extended Query）...")
        remote_file = self.start_capture('prepared_statement')
        
        operations = []
        
        try:
            conn = psycopg2.connect(**DB_CONFIG)
            cur = conn.cursor()
            
            # 创建测试表
            cur.execute("""
                CREATE TABLE IF NOT EXISTS prepared_test (
                    id SERIAL PRIMARY KEY,
                    product_name VARCHAR(100),
                    price DECIMAL(10,2),
                    quantity INTEGER
                )
            """)
            operations.append("CREATE TABLE prepared_test")
            
            # 创建预处理语句
            cur.execute("""
                PREPARE insert_product (varchar, decimal, integer) AS
                INSERT INTO prepared_test (product_name, price, quantity)
                VALUES ($1, $2, $3)
            """)
            operations.append("PREPARE insert_product statement")
            
            # 执行预处理语句多次
            products = [
                ('笔记本电脑', 5999.99, 10),
                ('无线鼠标', 99.50, 50),
                ('键盘', 299.00, 30),
                ('显示器', 1599.99, 15),
                ('耳机', 199.99, 25)
            ]
            
            for product_name, price, quantity in products:
                cur.execute("EXECUTE insert_product (%s, %s, %s)", 
                          (product_name, price, quantity))
                operations.append(f"EXECUTE insert_product: {product_name}")
            
            conn.commit()
            
            # 创建查询预处理语句
            cur.execute("""
                PREPARE select_by_price (decimal) AS
                SELECT * FROM prepared_test WHERE price > $1 ORDER BY price DESC
            """)
            operations.append("PREPARE select_by_price statement")
            
            # 执行查询预处理语句
            cur.execute("EXECUTE select_by_price (%s)", (200.0,))
            result = cur.fetchall()
            logger.info(f"价格>200的产品: {len(result)}个")
            operations.append("EXECUTE select_by_price with parameter 200.0")
            
            # 释放预处理语句
            cur.execute("DEALLOCATE insert_product")
            cur.execute("DEALLOCATE select_by_price")
            operations.append("DEALLOCATE all prepared statements")
            
            time.sleep(5)
            self.stop_capture()
            local_file = self.download_pcap(remote_file)
            
            self.report.add_capture('预处理语句测试', local_file, '\n'.join(operations))
            
            # 清理
            cur.execute("DROP TABLE prepared_test")
            conn.commit()
            
        except Exception as e:
            logger.error(f"预处理语句测试出错: {e}")
            raise e
        finally:
            if 'cur' in locals():
                cur.close()
            if 'conn' in locals():
                conn.close()

    def execute_batch_operations_test(self):
        """执行批量操作测试 - Extended Query协议"""
        logger.info("\n开始批量操作测试（Extended Query）...")
        remote_file = self.start_capture('batch_operations')
        
        operations = []
        
        try:
            conn = psycopg2.connect(**DB_CONFIG)
            cur = conn.cursor()
            
            # 创建测试表
            cur.execute("""
                CREATE TABLE IF NOT EXISTS batch_test (
                    id SERIAL PRIMARY KEY,
                    user_id INTEGER,
                    action VARCHAR(50),
                    timestamp TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
                    data JSONB
                )
            """)
            operations.append("CREATE TABLE batch_test")
            
            # 批量插入 - 使用executemany会产生Extended Query协议
            batch_data = []
            for i in range(50):
                user_id = random.randint(1, 10)
                actions = ['login', 'logout', 'click', 'scroll', 'purchase', 'view']
                action = random.choice(actions)
                data = {
                    'session_id': f'sess_{random.randint(1000, 9999)}', 
                    'ip': f'192.168.1.{random.randint(1, 254)}'
                }
                batch_data.append((user_id, action, data))
            
            cur.executemany("""
                INSERT INTO batch_test (user_id, action, data) 
                VALUES (%s, %s, %s)
            """, batch_data)
            operations.append(f"Batch INSERT: {len(batch_data)} records")
            conn.commit()
            
            # 批量查询 - 参数化IN查询
            user_ids = [1, 3, 5, 7, 9]
            placeholders = ','.join(['%s'] * len(user_ids))
            cur.execute(f"""
                SELECT user_id, action, COUNT(*) 
                FROM batch_test 
                WHERE user_id IN ({placeholders})
                GROUP BY user_id, action
                ORDER BY user_id, action
            """, user_ids)
            result = cur.fetchall()
            logger.info(f"批量查询结果: {len(result)}行")
            operations.append(f"Batch SELECT with IN clause: {user_ids}")
            
            time.sleep(5)
            self.stop_capture()
            local_file = self.download_pcap(remote_file)
            
            self.report.add_capture('批量操作测试', local_file, '\n'.join(operations))
            
            # 清理
            cur.execute("DROP TABLE batch_test")
            conn.commit()
            
        except Exception as e:
            logger.error(f"批量操作测试出错: {e}")
            raise e
        finally:
            if 'cur' in locals():
                cur.close()
            if 'conn' in locals():
                conn.close()

    def execute_cursor_operations_test(self):
        """执行游标操作测试 - Extended Query协议"""
        logger.info("\n开始游标操作测试（Extended Query）...")
        remote_file = self.start_capture('cursor_operations')
        
        operations = []
        
        try:
            conn = psycopg2.connect(**DB_CONFIG)
            cur = conn.cursor()
            
            # 创建测试表并插入数据
            cur.execute("""
                CREATE TABLE IF NOT EXISTS cursor_test (
                    id SERIAL PRIMARY KEY,
                    data VARCHAR(100)
                )
            """)
            operations.append("CREATE TABLE cursor_test")
            
            # 插入测试数据
            data_list = [(f'data_row_{i}',) for i in range(100)]
            cur.executemany("INSERT INTO cursor_test (data) VALUES (%s)", data_list)
            conn.commit()
            operations.append("INSERT 100 records")
            
            # 使用命名游标 - 这会产生Extended Query协议
            cursor_name = f"test_cursor_{int(time.time())}"
            
            # 声明游标
            cur.execute(f"""
                DECLARE {cursor_name} CURSOR FOR 
                SELECT id, data FROM cursor_test WHERE id > %s ORDER BY id
            """, (50,))
            operations.append(f"DECLARE CURSOR {cursor_name}")
            
            # 从游标获取数据
            cur.execute(f"FETCH 10 FROM {cursor_name}")
            result = cur.fetchall()
            logger.info(f"游标获取的前10行: {len(result)}行")
            operations.append("FETCH 10 FROM cursor")
            
            # 继续获取数据
            cur.execute(f"FETCH 15 FROM {cursor_name}")
            result = cur.fetchall()
            logger.info(f"游标获取的接下来15行: {len(result)}行")
            operations.append("FETCH 15 FROM cursor")
            
            # 关闭游标
            cur.execute(f"CLOSE {cursor_name}")
            operations.append("CLOSE cursor")
            
            time.sleep(5)
            self.stop_capture()
            local_file = self.download_pcap(remote_file)
            
            self.report.add_capture('游标操作测试', local_file, '\n'.join(operations))
            
            # 清理
            cur.execute("DROP TABLE cursor_test")
            conn.commit()
            
        except Exception as e:
            logger.error(f"游标操作测试出错: {e}")
            raise e
        finally:
            if 'cur' in locals():
                cur.close()
            if 'conn' in locals():
                conn.close()

    def execute_binary_format_test(self):
        """执行二进制格式测试 - Extended Query协议"""
        logger.info("\n开始二进制格式测试（Extended Query）...")
        remote_file = self.start_capture('binary_format')
        
        operations = []
        
        try:
            conn = psycopg2.connect(**DB_CONFIG)
            cur = conn.cursor()
            
            # 创建测试表
            cur.execute("""
                CREATE TABLE IF NOT EXISTS binary_test (
                    id SERIAL PRIMARY KEY,
                    binary_data BYTEA,
                    large_number BIGINT,
                    float_data DOUBLE PRECISION,
                    json_data JSONB
                )
            """)
            operations.append("CREATE TABLE binary_test")
            
            # 插入二进制数据
            for i in range(3):
                # 生成随机二进制数据
                binary_data = bytes([random.randint(0, 255) for _ in range(512)])
                large_number = random.randint(10**10, 10**12)
                float_data = random.uniform(0.0, 1000000.0)
                json_data = {'id': i, 'data': [random.randint(1, 100) for _ in range(5)]}
                
                cur.execute("""
                    INSERT INTO binary_test (binary_data, large_number, float_data, json_data)
                    VALUES (%s, %s, %s, %s)
                """, (binary_data, large_number, float_data, json_data))
                operations.append(f"INSERT binary data record {i+1}")
            
            conn.commit()
            
            # 查询二进制数据
            cur.execute("""
                SELECT id, length(binary_data), large_number, float_data 
                FROM binary_test 
                WHERE large_number > %s
            """, (5 * 10**10,))
            result = cur.fetchall()
            logger.info(f"二进制数据查询结果: {len(result)}行")
            operations.append("SELECT binary data with large_number filter")
            
            # 更新二进制数据
            new_binary = bytes([255 - b for b in range(256)])[:256]
            cur.execute("""
                UPDATE binary_test 
                SET binary_data = %s 
                WHERE id = %s
            """, (new_binary, 1))
            operations.append("UPDATE binary data for record 1")
            conn.commit()
            
            time.sleep(5)
            self.stop_capture()
            local_file = self.download_pcap(remote_file)
            
            self.report.add_capture('二进制格式测试', local_file, '\n'.join(operations))
            
            # 清理
            cur.execute("DROP TABLE binary_test")
            conn.commit()
            
        except Exception as e:
            logger.error(f"二进制格式测试出错: {e}")
            raise e
        finally:
            if 'cur' in locals():
                cur.close()
            if 'conn' in locals():
                conn.close()

    def execute_transaction_with_params_test(self):
        """执行带参数的事务测试 - Extended Query协议"""
        logger.info("\n开始带参数的事务测试（Extended Query）...")
        remote_file = self.start_capture('transaction_params')
        
        operations = []
        
        try:
            conn = psycopg2.connect(**DB_CONFIG)
            cur = conn.cursor()
            
            # 创建测试表
            cur.execute("""
                CREATE TABLE IF NOT EXISTS transaction_test (
                    id SERIAL PRIMARY KEY,
                    account_id INTEGER,
                    balance DECIMAL(15,2),
                    transaction_type VARCHAR(20),
                    amount DECIMAL(15,2)
                )
            """)
            operations.append("CREATE TABLE transaction_test")
            
            # 初始化账户
            initial_accounts = [(i, 1000.00) for i in range(1, 4)]
            cur.executemany("""
                INSERT INTO transaction_test (account_id, balance, transaction_type, amount)
                VALUES (%s, %s, 'initial', %s)
            """, [(acc_id, balance, balance) for acc_id, balance in initial_accounts])
            conn.commit()
            operations.append("Initialize 3 accounts with 1000.00 balance")
            
            # 开始事务
            cur.execute("BEGIN")
            operations.append("BEGIN transaction")
            
            # 设置保存点
            cur.execute("SAVEPOINT sp1")
            operations.append("SAVEPOINT sp1")
            
            # 转账操作 - 从账户1转100到账户2
            from_account = 1
            to_account = 2
            amount = 100.00
            
            # 检查余额
            cur.execute("""
                SELECT balance FROM transaction_test 
                WHERE account_id = %s 
                ORDER BY id DESC LIMIT 1
            """, (from_account,))
            from_balance = cur.fetchone()[0]
            operations.append(f"Check balance for account {from_account}")
            
            if from_balance >= amount:
                # 扣款
                cur.execute("""
                    INSERT INTO transaction_test (account_id, balance, transaction_type, amount)
                    VALUES (%s, %s, 'debit', %s)
                """, (from_account, from_balance - amount, -amount))
                operations.append(f"Debit {amount} from account {from_account}")
                
                # 入账
                cur.execute("""
                    SELECT balance FROM transaction_test 
                    WHERE account_id = %s 
                    ORDER BY id DESC LIMIT 1
                """, (to_account,))
                to_balance = cur.fetchone()[0]
                
                cur.execute("""
                    INSERT INTO transaction_test (account_id, balance, transaction_type, amount)
                    VALUES (%s, %s, 'credit', %s)
                """, (to_account, to_balance + amount, amount))
                operations.append(f"Credit {amount} to account {to_account}")
                
                # 提交到保存点
                cur.execute("RELEASE SAVEPOINT sp1")
                operations.append("RELEASE SAVEPOINT sp1")
            
            # 提交事务
            cur.execute("COMMIT")
            operations.append("COMMIT transaction")
            
            # 查看最终结果
            cur.execute("SELECT account_id, balance, transaction_type, amount FROM transaction_test ORDER BY account_id, id")
            result = cur.fetchall()
            logger.info(f"事务完成，共{len(result)}条记录")
            operations.append("SELECT final results")
            
            time.sleep(5)
            self.stop_capture()
            local_file = self.download_pcap(remote_file)
            
            self.report.add_capture('带参数的事务测试', local_file, '\n'.join(operations))
            
            # 清理
            cur.execute("DROP TABLE transaction_test")
            conn.commit()
            
        except Exception as e:
            logger.error(f"带参数的事务测试出错: {e}")
            if 'conn' in locals():
                conn.rollback()
            raise e
        finally:
            if 'cur' in locals():
                cur.close()
            if 'conn' in locals():
                conn.close()

    def execute_json_operations_test(self):
        """执行JSON操作测试 - Extended Query协议"""
        logger.info("\n开始JSON操作测试（Extended Query）...")
        remote_file = self.start_capture('json_operations')
        
        operations = []
        
        try:
            conn = psycopg2.connect(**DB_CONFIG)
            cur = conn.cursor()
            
            # 创建测试表
            cur.execute("""
                CREATE TABLE IF NOT EXISTS json_test (
                    id SERIAL PRIMARY KEY,
                    user_profile JSONB,
                    settings JSON
                )
            """)
            operations.append("CREATE TABLE json_test")
            
            # 插入JSON数据
            json_data = [
                {
                    'user_profile': {'name': '张三', 'age': 25, 'skills': ['Python', 'JavaScript']},
                    'settings': {'theme': 'dark', 'language': 'zh-CN'}
                },
                {
                    'user_profile': {'name': '李四', 'age': 30, 'skills': ['Java', 'Spring']},
                    'settings': {'theme': 'light', 'language': 'en-US'}
                },
                {
                    'user_profile': {'name': '王五', 'age': 28, 'skills': ['Go', 'Docker']},
                    'settings': {'theme': 'auto', 'language': 'zh-CN'}
                }
            ]
            
            for data in json_data:
                cur.execute("""
                    INSERT INTO json_test (user_profile, settings)
                    VALUES (%s, %s)
                """, (data['user_profile'], data['settings']))
                operations.append(f"INSERT JSON data for {data['user_profile']['name']}")
            
            conn.commit()
            
            # JSON查询操作
            # 1. 根据JSON字段查询
            cur.execute("""
                SELECT id, user_profile->>'name' as name, user_profile->>'age' as age
                FROM json_test
                WHERE (user_profile->>'age')::int > %s
            """, (25,))
            result = cur.fetchall()
            logger.info(f"年龄>25的用户: {len(result)}个")
            operations.append("SELECT users with age > 25 using JSON operators")
            
            # 2. JSON数组查询
            cur.execute("""
                SELECT user_profile->>'name' as name, user_profile->'skills' as skills
                FROM json_test
                WHERE user_profile->'skills' ? %s
            """, ('Python',))
            result = cur.fetchall()
            operations.append("SELECT users with Python skill using JSON ? operator")
            
            # 3. JSON更新
            cur.execute("""
                UPDATE json_test
                SET user_profile = jsonb_set(user_profile, '{age}', %s)
                WHERE user_profile->>'name' = %s
            """, ('26', '张三'))
            operations.append("UPDATE JSON field using jsonb_set")
            
            conn.commit()
            
            time.sleep(5)
            self.stop_capture()
            local_file = self.download_pcap(remote_file)
            
            self.report.add_capture('JSON操作测试', local_file, '\n'.join(operations))
            
            # 清理
            cur.execute("DROP TABLE json_test")
            conn.commit()
            
        except Exception as e:
            logger.error(f"JSON操作测试出错: {e}")
            raise e
        finally:
            if 'cur' in locals():
                cur.close()
            if 'conn' in locals():
                conn.close()

    def cleanup(self):
        """清理资源"""
        if self.sftp:
            self.sftp.close()
        if self.ssh:
            self.ssh.close()

def main():
    """主函数"""
    print("=== PostgreSQL Extended Query协议流量抓包脚本 ===")
    print("本脚本专门使用Extended Query协议（Parse/Bind/Execute）生成PostgreSQL流量")
    
    capture = ExtendedQueryCapture()
    
    try:
        # 执行各种Extended Query测试
        capture.execute_parameterized_query_test()
        capture.execute_prepared_statement_test()
        capture.execute_batch_operations_test()
        capture.execute_cursor_operations_test()
        capture.execute_binary_format_test()
        capture.execute_transaction_with_params_test()
        capture.execute_json_operations_test()
        
        # 生成报告
        report_file = capture.report.generate_report()
        print(f"\n抓包完成！报告已生成: {report_file}")
        
        # 显示统计信息
        print(f"总共生成了 {len(capture.report.captures)} 个抓包文件")
        total_size = sum(c['file_size'] for c in capture.report.captures)
        print(f"总文件大小: {total_size / (1024*1024):.2f}MB")
        
        print("\n=== Extended Query协议说明 ===")
        print("本脚本生成的所有流量都使用Extended Query协议，包含以下消息类型：")
        print("- Parse (P): 解析SQL语句，创建预处理语句")
        print("- Bind (B): 将参数绑定到预处理语句")
        print("- Execute (E): 执行绑定的语句")
        print("- Sync (S): 同步请求")
        print("- Describe (D): 描述语句或门户")
        print("- Close (C): 关闭语句或门户")
        
    except Exception as e:
        logger.error(f"执行过程中出错: {e}")
        sys.exit(1)
    finally:
        capture.cleanup()

if __name__ == "__main__":
    main() 