#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
PostgreSQL Extended Query协议官方规范测试
严格按照PostgreSQL官方文档实现Extended Query协议测试用例
参考: https://www.postgresql.org/docs/current/protocol-flow.html#PROTOCOL-FLOW-EXT-QUERY

与java_jdbc_postgresql_traffic_capture.py保持一致的抓包流程和配置
"""
import psycopg
import paramiko
import time
import os
from datetime import datetime, date, timedelta
from datetime import time as dt_time
import sys
from pathlib import Path
import logging
import json
import uuid
import decimal
from typing import List, Dict, Any, Optional
import threading
import concurrent.futures
from contextlib import contextmanager

# 配置日志
logging.basicConfig(
    level=logging.INFO,
    format='%(asctime)s - %(levelname)s - %(message)s'
)
logger = logging.getLogger(__name__)

# 尝试导入外部配置文件，如果不存在则使用默认配置
try:
    from config import SERVER_CONFIG, DB_CONFIG, CAPTURE_CONFIG
    logger.info("使用外部配置文件 config.py")
except ImportError:
    logger.info("未找到 config.py，使用默认配置")
    # 服务器配置
    SERVER_CONFIG = {
        'hostname': '**************',
        'username': 'root',
        'password': 'root@123',
        'port': 22
    }

    # 数据库配置 - 严格按照Extended Query协议要求
    DB_CONFIG = {
        'host': '**************',
        'port': 5432,
        'dbname': 'postgres',
        'user': 'postgres',
        'password': 'mysecretpassword',
        'prepare_threshold': 1,  # 强制使用prepared statements (Extended Query)
        # 移除options参数以避免配置问题
    }

    # 抓包配置
    CAPTURE_CONFIG = {
        'interface': 'eth0',
        'port': 5432,
        'remote_dir': '/root/pcap',
        'local_dir': './postgresql_extended_query_official_pcap'
    }

class PostgreSQLExtendedQueryOfficialSpec:
    """
    PostgreSQL Extended Query协议官方规范测试类
    严格按照官方文档实现Extended Query协议的完整流程
    """
    
    def __init__(self):
        self.ssh = None
        self.sftp = None
        self.connect_ssh()
        self.setup_directories()
        self.test_results = []
        
    def connect_ssh(self):
        """连接SSH服务器"""
        try:
            self.ssh = paramiko.SSHClient()
            self.ssh.set_missing_host_key_policy(paramiko.AutoAddPolicy())
            self.ssh.connect(**SERVER_CONFIG)
            self.sftp = self.ssh.open_sftp()
            logger.info("SSH连接成功")
        except Exception as e:
            logger.error(f"SSH连接失败: {e}")
            sys.exit(1)

    def setup_directories(self):
        """设置远程和本地目录"""
        self.ssh.exec_command(f"mkdir -p {CAPTURE_CONFIG['remote_dir']}")
        Path(CAPTURE_CONFIG['local_dir']).mkdir(parents=True, exist_ok=True)

    def start_capture(self, capture_type):
        """开始抓包"""
        timestamp = datetime.now().strftime('%Y%m%d_%H%M%S')
        remote_file = f"{CAPTURE_CONFIG['remote_dir']}/extended_query_{capture_type}_{timestamp}.pcap"
        cmd = f"tcpdump -i {CAPTURE_CONFIG['interface']} -s 0 -w {remote_file} port {CAPTURE_CONFIG['port']}"
        logger.info(f"开始抓包: {capture_type}")
        self.ssh.exec_command(f"nohup {cmd} > /dev/null 2>&1 &")
        time.sleep(2)
        return remote_file

    def stop_capture(self):
        """停止抓包"""
        logger.info("停止抓包...")
        self.ssh.exec_command("pkill tcpdump")
        time.sleep(2)

    def download_pcap(self, remote_file):
        """下载pcap文件到本地"""
        filename = os.path.basename(remote_file)
        local_file = os.path.join(CAPTURE_CONFIG['local_dir'], filename)
        self.sftp.get(remote_file, local_file)
        logger.info(f"已下载: {filename}")
        return local_file

    @contextmanager
    def get_connection(self, **kwargs):
        """获取数据库连接的上下文管理器 - 强制Extended Query协议"""
        config = DB_CONFIG.copy()
        config.update(kwargs)
        # 确保强制使用Extended Query协议
        config['prepare_threshold'] = 1
        conn = None
        try:
            conn = psycopg.connect(**config)
            # 设置自动提交为False，模拟标准事务行为
            conn.autocommit = False
            yield conn
        except Exception as e:
            if conn:
                conn.rollback()
            raise e
        finally:
            if conn:
                conn.close()

    def execute_test_with_capture(self, test_name: str, test_func):
        """执行测试并抓包"""
        logger.info(f"\n=== 开始 {test_name} 测试 ===")
        remote_file = self.start_capture(test_name.lower().replace(' ', '_'))
        
        try:
            operations = test_func()
            time.sleep(3)  # 等待网络包传输完成
            self.stop_capture()
            local_file = self.download_pcap(remote_file)
            
            self.test_results.append({
                'test_name': test_name,
                'pcap_file': local_file,
                'operations': operations,
                'status': 'SUCCESS',
                'timestamp': datetime.now().isoformat()
            })
            logger.info(f"{test_name} 测试完成")
            
        except Exception as e:
            logger.error(f"{test_name} 测试失败: {e}")
            self.stop_capture()
            try:
                local_file = self.download_pcap(remote_file)
                self.test_results.append({
                    'test_name': test_name,
                    'pcap_file': local_file,
                    'operations': [],
                    'status': 'FAILED',
                    'error': str(e),
                    'timestamp': datetime.now().isoformat()
                })
            except:
                pass

    def test_parse_bind_execute_sync_cycle(self):
        """
        测试完整的Parse-Bind-Execute-Sync循环
        严格按照官方文档的Extended Query协议流程
        """
        operations = []
        
        with self.get_connection() as conn:
            operations.append("开始Parse-Bind-Execute-Sync循环测试")
            
            # 1. Parse阶段 - 解析SQL语句并创建prepared statement
            operations.append("Parse阶段: 创建prepared statement")
            with conn.cursor() as cur:
                # 创建测试表
                cur.execute("""
                    CREATE TABLE IF NOT EXISTS extended_query_test (
                        id SERIAL PRIMARY KEY,
                        name VARCHAR(100),
                        age INTEGER,
                        salary DECIMAL(10,2),
                        created_at TIMESTAMP DEFAULT NOW(),
                        is_active BOOLEAN DEFAULT true
                    )
                """)
                
                # 清理旧数据
                cur.execute("DELETE FROM extended_query_test")
                conn.commit()
                
            # 2. 多个Parse-Bind-Execute-Sync循环
            operations.append("执行多个完整的Extended Query循环")
            
            with conn.cursor() as cur:
                # 第一个循环：插入数据
                operations.append("循环1: Parse-Bind-Execute-Sync (INSERT)")
                insert_sql = """
                    INSERT INTO extended_query_test (name, age, salary, is_active) 
                    VALUES (%s, %s, %s, %s)
                """
                
                # Parse: SQL被解析并准备
                # Bind: 参数被绑定
                # Execute: 语句被执行
                # Sync: 隐式调用（psycopg3自动处理）
                test_data = [
                    ("Alice Johnson", 30, decimal.Decimal('75000.00'), True),
                    ("Bob Smith", 25, decimal.Decimal('65000.00'), True),
                    ("Carol Davis", 35, decimal.Decimal('85000.00'), False),
                    ("David Wilson", 28, decimal.Decimal('70000.00'), True),
                    ("Eve Brown", 32, decimal.Decimal('80000.00'), True)
                ]
                
                for data in test_data:
                    cur.execute(insert_sql, data)
                    operations.append(f"Parse-Bind-Execute: 插入 {data[0]}")
                
                conn.commit()  # Sync点
                operations.append("Sync: 事务提交")
                
                # 第二个循环：查询数据
                operations.append("循环2: Parse-Bind-Execute-Sync (SELECT)")
                select_sql = """
                    SELECT name, age, salary FROM extended_query_test 
                    WHERE age BETWEEN %s AND %s AND is_active = %s
                    ORDER BY salary DESC
                """
                
                # Parse-Bind-Execute-Sync循环
                cur.execute(select_sql, (25, 35, True))
                results = cur.fetchall()
                operations.append(f"Parse-Bind-Execute: 查询返回 {len(results)} 条记录")
                
                for row in results:
                    logger.info(f"  查询结果: {row[0]}, 年龄{row[1]}, 薪资{row[2]}")
                
                # 第三个循环：更新数据
                operations.append("循环3: Parse-Bind-Execute-Sync (UPDATE)")
                update_sql = """
                    UPDATE extended_query_test 
                    SET salary = salary * %s 
                    WHERE age > %s AND is_active = %s
                """
                
                cur.execute(update_sql, (decimal.Decimal('1.1'), 30, True))
                updated_count = cur.rowcount
                operations.append(f"Parse-Bind-Execute: 更新了 {updated_count} 条记录")
                
                conn.commit()  # Sync点
                operations.append("Sync: 更新事务提交")
                
                # 第四个循环：删除数据
                operations.append("循环4: Parse-Bind-Execute-Sync (DELETE)")
                delete_sql = """
                    DELETE FROM extended_query_test 
                    WHERE is_active = %s
                """
                
                cur.execute(delete_sql, (False,))
                deleted_count = cur.rowcount
                operations.append(f"Parse-Bind-Execute: 删除了 {deleted_count} 条记录")
                
                conn.commit()  # Sync点
                operations.append("Sync: 删除事务提交")
                
            # 清理测试表
            with conn.cursor() as cur:
                cur.execute("DROP TABLE IF EXISTS extended_query_test")
                conn.commit()
                operations.append("清理: 删除测试表")
                
        return operations

    def test_prepared_statement_reuse(self):
        """
        测试prepared statement的重用
        按照官方文档，prepared statement可以被多次Bind和Execute
        """
        operations = []
        
        with self.get_connection() as conn:
            operations.append("开始prepared statement重用测试")
            
            with conn.cursor() as cur:
                # 创建测试表
                cur.execute("""
                    CREATE TABLE IF NOT EXISTS statement_reuse_test (
                        id SERIAL PRIMARY KEY,
                        category VARCHAR(50),
                        value INTEGER,
                        description TEXT
                    )
                """)
                conn.commit()
                
                # 准备一个可重用的插入语句
                operations.append("Parse: 创建可重用的prepared statement")
                insert_sql = """
                    INSERT INTO statement_reuse_test (category, value, description) 
                    VALUES (%s, %s, %s)
                """
                
                # 多次Bind-Execute同一个prepared statement
                operations.append("多次Bind-Execute重用prepared statement")
                test_categories = ['A', 'B', 'C', 'D', 'E']
                
                for i, category in enumerate(test_categories):
                    for j in range(5):  # 每个类别插入5条记录
                        # Bind: 绑定新的参数值
                        # Execute: 执行prepared statement
                        cur.execute(insert_sql, (category, (j + 1) * 10, f"Description for {category}{j+1}"))
                        operations.append(f"Bind-Execute: {category}{j+1}")
                
                conn.commit()  # Sync
                operations.append("Sync: 批量插入提交")
                
                # 准备一个可重用的查询语句
                operations.append("Parse: 创建可重用的查询prepared statement")
                select_sql = """
                    SELECT category, COUNT(*) as count, AVG(value) as avg_value
                    FROM statement_reuse_test 
                    WHERE category = %s
                    GROUP BY category
                """
                
                # 多次使用同一个查询语句查询不同类别
                operations.append("多次重用查询prepared statement")
                for category in test_categories:
                    # Bind-Execute循环
                    cur.execute(select_sql, (category,))
                    result = cur.fetchone()
                    if result:
                        logger.info(f"类别 {result[0]}: 数量={result[1]}, 平均值={result[2]:.2f}")
                        operations.append(f"Bind-Execute查询: {category} -> {result[1]}条记录")
                
            # 清理测试表
            with conn.cursor() as cur:
                cur.execute("DROP TABLE IF EXISTS statement_reuse_test")
                conn.commit()
                
        return operations

    def test_portal_operations(self):
        """
        测试Portal操作
        按照官方文档，Portal是Bind操作的结果，可以被Execute多次
        """
        operations = []
        
        with self.get_connection() as conn:
            operations.append("开始Portal操作测试")
            
            with conn.cursor() as cur:
                # 创建测试数据
                cur.execute("""
                    CREATE TABLE IF NOT EXISTS portal_test (
                        id SERIAL PRIMARY KEY,
                        data VARCHAR(100)
                    )
                """)
                
                # 插入测试数据
                for i in range(100):
                    cur.execute("INSERT INTO portal_test (data) VALUES (%s)", (f"Data_{i+1:03d}",))
                
                conn.commit()
                operations.append("准备Portal测试数据: 100条记录")
                
                # 测试Portal的分批获取（模拟FETCH操作）
                operations.append("Parse-Bind创建Portal")
                
                # 使用cursor的fetchmany来模拟Portal的分批执行
                cur.execute("SELECT id, data FROM portal_test ORDER BY id")
                operations.append("Execute Portal: 开始分批获取")
                
                batch_size = 20
                batch_count = 0
                while True:
                    # 模拟Execute消息的row-count限制
                    rows = cur.fetchmany(batch_size)
                    if not rows:
                        break
                        
                    batch_count += 1
                    operations.append(f"Execute Portal批次{batch_count}: 获取{len(rows)}条记录")
                    logger.info(f"Portal批次 {batch_count}: 获取了 {len(rows)} 条记录")
                    
                    # 显示前几条记录
                    for i, row in enumerate(rows[:3]):
                        logger.info(f"  记录 {row[0]}: {row[1]}")
                    
                    if len(rows) < batch_size:
                        break
                
                operations.append(f"Portal操作完成: 总共{batch_count}个批次")
                
            # 清理测试表
            with conn.cursor() as cur:
                cur.execute("DROP TABLE IF EXISTS portal_test")
                conn.commit()
                
        return operations

    def test_parameter_data_types(self):
        """
        测试参数数据类型处理
        按照官方文档，Extended Query支持指定参数的OID类型
        """
        operations = []

        with self.get_connection() as conn:
            operations.append("开始参数数据类型测试")

            with conn.cursor() as cur:
                # 创建包含各种数据类型的测试表
                cur.execute("""
                    CREATE TABLE IF NOT EXISTS datatype_param_test (
                        id SERIAL PRIMARY KEY,
                        int_val INTEGER,
                        bigint_val BIGINT,
                        decimal_val DECIMAL(10,2),
                        text_val TEXT,
                        bool_val BOOLEAN,
                        date_val DATE,
                        timestamp_val TIMESTAMP,
                        json_val JSONB,
                        array_val INTEGER[]
                    )
                """)
                conn.commit()

                # 测试各种参数类型的Parse-Bind-Execute
                operations.append("Parse-Bind-Execute: 各种参数类型")

                # 插入包含各种数据类型的记录
                insert_sql = """
                    INSERT INTO datatype_param_test
                    (int_val, bigint_val, decimal_val, text_val, bool_val,
                     date_val, timestamp_val, json_val, array_val)
                    VALUES (%s, %s, %s, %s, %s, %s, %s, %s, %s)
                """

                test_data = (
                    42,  # INTEGER
                    9223372036854775807,  # BIGINT
                    decimal.Decimal('12345.67'),  # DECIMAL
                    'Extended Query Test String',  # TEXT
                    True,  # BOOLEAN
                    date(2023, 12, 25),  # DATE
                    datetime(2023, 12, 25, 14, 30, 0),  # TIMESTAMP
                    json.dumps({"type": "extended_query", "version": 1}),  # JSONB
                    [1, 2, 3, 4, 5]  # INTEGER[]
                )

                cur.execute(insert_sql, test_data)
                operations.append("Bind-Execute: 插入多种数据类型")
                conn.commit()

                # 测试参数化查询各种数据类型
                operations.append("Parse-Bind-Execute: 参数化查询各种类型")

                # 查询INTEGER参数
                cur.execute("SELECT * FROM datatype_param_test WHERE int_val = %s", (42,))
                result = cur.fetchone()
                operations.append(f"INTEGER参数查询: 找到记录ID {result[0]}")

                # 查询TEXT参数
                cur.execute("SELECT * FROM datatype_param_test WHERE text_val LIKE %s", ('%Extended%',))
                result = cur.fetchone()
                operations.append(f"TEXT参数查询: 匹配文本记录")

                # 查询BOOLEAN参数
                cur.execute("SELECT * FROM datatype_param_test WHERE bool_val = %s", (True,))
                result = cur.fetchone()
                operations.append(f"BOOLEAN参数查询: 找到真值记录")

                # 查询DATE参数
                cur.execute("SELECT * FROM datatype_param_test WHERE date_val = %s", (date(2023, 12, 25),))
                result = cur.fetchone()
                operations.append(f"DATE参数查询: 找到日期记录")

                # 查询ARRAY参数
                cur.execute("SELECT * FROM datatype_param_test WHERE %s = ANY(array_val)", (3,))
                result = cur.fetchone()
                operations.append(f"ARRAY参数查询: 找到包含元素的记录")

            # 清理测试表
            with conn.cursor() as cur:
                cur.execute("DROP TABLE IF EXISTS datatype_param_test")
                conn.commit()

        return operations

    def test_transaction_control_with_extended_query(self):
        """
        测试Extended Query中的事务控制
        按照官方文档，Sync消息会在非事务块中触发隐式提交/回滚
        """
        operations = []

        with self.get_connection() as conn:
            operations.append("开始Extended Query事务控制测试")

            with conn.cursor() as cur:
                # 创建测试表
                cur.execute("""
                    CREATE TABLE IF NOT EXISTS transaction_control_test (
                        id SERIAL PRIMARY KEY,
                        operation VARCHAR(50),
                        value INTEGER
                    )
                """)
                conn.commit()

                # 测试1: 显式事务块中的Extended Query
                operations.append("测试1: 显式事务块中的Parse-Bind-Execute")

                # BEGIN - 开始显式事务
                cur.execute("BEGIN")
                operations.append("BEGIN: 开始显式事务块")

                # 在事务中执行多个Extended Query操作
                for i in range(3):
                    cur.execute(
                        "INSERT INTO transaction_control_test (operation, value) VALUES (%s, %s)",
                        (f"explicit_tx_{i+1}", (i+1) * 10)
                    )
                    operations.append(f"Parse-Bind-Execute: 事务中插入操作{i+1}")

                # COMMIT - 提交事务
                cur.execute("COMMIT")
                operations.append("COMMIT: 提交显式事务块")

                # 验证数据已提交
                cur.execute("SELECT COUNT(*) FROM transaction_control_test WHERE operation LIKE %s", ('explicit_tx_%',))
                count = cur.fetchone()[0]
                operations.append(f"验证: 显式事务提交了{count}条记录")

                # 测试2: 隐式事务（每个Sync自动提交）
                operations.append("测试2: 隐式事务中的Parse-Bind-Execute")

                # 不使用BEGIN，每个操作都是隐式事务
                for i in range(3):
                    cur.execute(
                        "INSERT INTO transaction_control_test (operation, value) VALUES (%s, %s)",
                        (f"implicit_tx_{i+1}", (i+1) * 20)
                    )
                    # psycopg3会在每个execute后隐式Sync（如果不在显式事务中）
                    operations.append(f"Parse-Bind-Execute-Sync: 隐式事务操作{i+1}")

                # 验证隐式事务的数据
                cur.execute("SELECT COUNT(*) FROM transaction_control_test WHERE operation LIKE %s", ('implicit_tx_%',))
                count = cur.fetchone()[0]
                operations.append(f"验证: 隐式事务提交了{count}条记录")

                # 测试3: 事务回滚
                operations.append("测试3: Extended Query事务回滚")

                try:
                    cur.execute("BEGIN")
                    operations.append("BEGIN: 开始回滚测试事务")

                    # 插入一些数据
                    cur.execute(
                        "INSERT INTO transaction_control_test (operation, value) VALUES (%s, %s)",
                        ("rollback_test", 999)
                    )
                    operations.append("Parse-Bind-Execute: 插入待回滚数据")

                    # 故意触发错误来测试回滚
                    cur.execute("SELECT 1/0")  # 除零错误

                except Exception as e:
                    cur.execute("ROLLBACK")
                    operations.append(f"ROLLBACK: 回滚事务 - {type(e).__name__}")

                # 验证回滚效果
                cur.execute("SELECT COUNT(*) FROM transaction_control_test WHERE operation = %s", ('rollback_test',))
                count = cur.fetchone()[0]
                operations.append(f"验证: 回滚后数据条数为{count}（应为0）")

            # 清理测试表
            with conn.cursor() as cur:
                cur.execute("DROP TABLE IF EXISTS transaction_control_test")
                conn.commit()

        return operations

    def test_error_handling_in_extended_query(self):
        """
        测试Extended Query中的错误处理
        按照官方文档，错误后backend会跳过消息直到Sync
        """
        operations = []

        with self.get_connection() as conn:
            operations.append("开始Extended Query错误处理测试")

            with conn.cursor() as cur:
                # 创建测试表
                cur.execute("""
                    CREATE TABLE IF NOT EXISTS error_handling_test (
                        id SERIAL PRIMARY KEY,
                        name VARCHAR(50) UNIQUE NOT NULL,
                        value INTEGER CHECK (value > 0)
                    )
                """)
                conn.commit()

                # 测试1: Parse阶段的语法错误
                operations.append("测试1: Parse阶段语法错误")
                try:
                    cur.execute("SELCT * FROM error_handling_test")  # 故意的语法错误
                except Exception as e:
                    operations.append(f"Parse错误捕获: {type(e).__name__}")
                    conn.rollback()  # 相当于Sync后的错误恢复

                # 测试2: Bind阶段的参数类型错误
                operations.append("测试2: Bind阶段参数类型错误")
                try:
                    cur.execute("SELECT %s::integer", ("not_a_number",))
                except Exception as e:
                    operations.append(f"Bind错误捕获: {type(e).__name__}")
                    conn.rollback()

                # 测试3: Execute阶段的约束违反错误
                operations.append("测试3: Execute阶段约束违反")
                try:
                    # 先插入一条正常记录
                    cur.execute("INSERT INTO error_handling_test (name, value) VALUES (%s, %s)", ("test1", 100))
                    conn.commit()

                    # 尝试插入重复的name（违反UNIQUE约束）
                    cur.execute("INSERT INTO error_handling_test (name, value) VALUES (%s, %s)", ("test1", 200))
                    conn.commit()
                except Exception as e:
                    operations.append(f"Execute约束错误捕获: {type(e).__name__}")
                    conn.rollback()

                # 测试4: CHECK约束错误
                operations.append("测试4: CHECK约束错误")
                try:
                    cur.execute("INSERT INTO error_handling_test (name, value) VALUES (%s, %s)", ("test2", -50))
                    conn.commit()
                except Exception as e:
                    operations.append(f"CHECK约束错误捕获: {type(e).__name__}")
                    conn.rollback()

                # 测试5: 错误恢复后的正常操作
                operations.append("测试5: 错误恢复后继续正常操作")
                try:
                    cur.execute("INSERT INTO error_handling_test (name, value) VALUES (%s, %s)", ("test_recovery", 300))
                    conn.commit()
                    operations.append("错误恢复成功: 正常插入记录")

                    # 验证数据
                    cur.execute("SELECT COUNT(*) FROM error_handling_test")
                    count = cur.fetchone()[0]
                    operations.append(f"验证: 表中共有{count}条记录")

                except Exception as e:
                    operations.append(f"错误恢复失败: {e}")
                    conn.rollback()

            # 清理测试表
            with conn.cursor() as cur:
                cur.execute("DROP TABLE IF EXISTS error_handling_test")
                conn.commit()

        return operations

    def test_pipelining_extended_query(self):
        """
        测试Extended Query的流水线操作
        按照官方文档，可以发送多个查询而不等待前面的完成
        """
        operations = []

        with self.get_connection() as conn:
            operations.append("开始Extended Query流水线测试")

            with conn.cursor() as cur:
                # 创建测试表
                cur.execute("""
                    CREATE TABLE IF NOT EXISTS pipeline_test (
                        id SERIAL PRIMARY KEY,
                        step VARCHAR(50),
                        data VARCHAR(100)
                    )
                """)
                conn.commit()

                # 模拟流水线操作：连续发送多个Parse-Bind-Execute而不等待
                operations.append("流水线操作: 连续发送多个Extended Query")

                # 准备多个操作
                pipeline_operations = [
                    ("step1", "Pipeline operation 1"),
                    ("step2", "Pipeline operation 2"),
                    ("step3", "Pipeline operation 3"),
                    ("step4", "Pipeline operation 4"),
                    ("step5", "Pipeline operation 5")
                ]

                # 批量执行（psycopg3内部会优化为流水线）
                insert_sql = "INSERT INTO pipeline_test (step, data) VALUES (%s, %s)"

                for step, data in pipeline_operations:
                    cur.execute(insert_sql, (step, data))
                    operations.append(f"Pipeline Parse-Bind-Execute: {step}")

                # Sync点 - 提交所有操作
                conn.commit()
                operations.append("Pipeline Sync: 提交所有流水线操作")

                # 验证流水线操作结果
                cur.execute("SELECT COUNT(*) FROM pipeline_test")
                count = cur.fetchone()[0]
                operations.append(f"流水线验证: 成功插入{count}条记录")

                # 流水线查询操作
                operations.append("流水线查询: 连续查询操作")
                for step, _ in pipeline_operations:
                    cur.execute("SELECT data FROM pipeline_test WHERE step = %s", (step,))
                    result = cur.fetchone()
                    operations.append(f"Pipeline查询: {step} -> {result[0] if result else 'Not found'}")

            # 清理测试表
            with conn.cursor() as cur:
                cur.execute("DROP TABLE IF EXISTS pipeline_test")
                conn.commit()

        return operations

    def generate_comprehensive_report(self):
        """生成全面的测试报告"""
        report_content = f"""# PostgreSQL Extended Query协议官方规范测试报告

## 测试概要
- 测试时间: {datetime.now().strftime('%Y-%m-%d %H:%M:%S')}
- 测试用例总数: {len(self.test_results)}
- 成功测试: {len([r for r in self.test_results if r['status'] == 'SUCCESS'])}
- 失败测试: {len([r for r in self.test_results if r['status'] == 'FAILED'])}

## 技术规范
- 严格按照PostgreSQL官方文档实现Extended Query协议
- 参考文档: https://www.postgresql.org/docs/current/protocol-flow.html#PROTOCOL-FLOW-EXT-QUERY
- 强制使用prepare_threshold=1确保Extended Query协议
- 完整的Parse → Bind → Execute → Sync流程

## 测试结果详情

"""

        for result in self.test_results:
            report_content += f"""### {result['test_name']}
- 状态: {result['status']}
- 时间: {result['timestamp']}
- PCAP文件: {os.path.basename(result['pcap_file'])}
- 操作数量: {len(result['operations'])}

"""
            if result['status'] == 'FAILED':
                report_content += f"- 错误信息: {result.get('error', 'Unknown error')}\n\n"
            else:
                report_content += f"- 执行的操作:\n"
                for i, op in enumerate(result['operations'][:5], 1):
                    report_content += f"  {i}. {op}\n"
                if len(result['operations']) > 5:
                    report_content += f"  ... 还有 {len(result['operations']) - 5} 个操作\n"
                report_content += "\n"

        # 保存报告
        report_file = os.path.join(CAPTURE_CONFIG['local_dir'],
                                 f"extended_query_official_spec_report_{datetime.now().strftime('%Y%m%d_%H%M%S')}.md")
        with open(report_file, 'w', encoding='utf-8') as f:
            f.write(report_content)

        logger.info(f"测试报告已生成: {report_file}")
        return report_file

    def cleanup(self):
        """清理资源"""
        if self.sftp:
            self.sftp.close()
        if self.ssh:
            self.ssh.close()

def main():
    """主函数 - 执行所有PostgreSQL Extended Query官方规范测试"""
    capture = PostgreSQLExtendedQueryOfficialSpec()

    try:
        # 定义所有测试用例 - 严格按照PostgreSQL官方文档的Extended Query协议
        test_cases = [
            ("Parse-Bind-Execute-Sync循环", capture.test_parse_bind_execute_sync_cycle),
            ("Prepared Statement重用", capture.test_prepared_statement_reuse),
            ("Portal操作", capture.test_portal_operations),
            ("参数数据类型", capture.test_parameter_data_types),
            ("事务控制", capture.test_transaction_control_with_extended_query),
            ("错误处理", capture.test_error_handling_in_extended_query),
            ("流水线操作", capture.test_pipelining_extended_query)
        ]

        logger.info(f"开始执行 {len(test_cases)} 个PostgreSQL Extended Query官方规范测试")
        logger.info("=" * 80)
        logger.info("PostgreSQL Extended Query协议官方规范测试")
        logger.info("参考文档: https://www.postgresql.org/docs/current/protocol-flow.html#PROTOCOL-FLOW-EXT-QUERY")
        logger.info("=" * 80)
        logger.info("技术特点:")
        logger.info("- 严格按照PostgreSQL官方文档实现")
        logger.info("- 强制使用Extended Query协议 (prepare_threshold=1)")
        logger.info("- 完整的Parse → Bind → Execute → Sync流程")
        logger.info("- 涵盖官方文档中的所有核心功能")
        logger.info("- 与java_jdbc_postgresql_traffic_capture.py保持一致的抓包流程")
        logger.info("=" * 80)

        # 执行所有测试
        for test_name, test_func in test_cases:
            try:
                capture.execute_test_with_capture(test_name, test_func)
                time.sleep(2)  # 测试间隔
            except Exception as e:
                logger.error(f"测试 {test_name} 执行失败: {e}")
                continue

        # 生成报告
        report_file = capture.generate_comprehensive_report()

        logger.info("\n" + "=" * 80)
        logger.info("PostgreSQL Extended Query协议官方规范测试完成！")
        logger.info(f"测试报告: {report_file}")
        logger.info(f"PCAP文件保存在: {CAPTURE_CONFIG['local_dir']}")
        logger.info("=" * 80)

        # 显示测试摘要
        success_count = len([r for r in capture.test_results if r['status'] == 'SUCCESS'])
        failed_count = len([r for r in capture.test_results if r['status'] == 'FAILED'])

        logger.info(f"测试摘要:")
        logger.info(f"  总测试数: {len(capture.test_results)}")
        logger.info(f"  成功: {success_count}")
        logger.info(f"  失败: {failed_count}")
        logger.info(f"  成功率: {(success_count/len(capture.test_results)*100):.1f}%")

        logger.info("\n协议验证要点:")
        logger.info("- 所有操作都使用Extended Query协议")
        logger.info("- Parse消息: SQL语句解析和预编译")
        logger.info("- Bind消息: 参数绑定到prepared statement")
        logger.info("- Execute消息: 执行绑定后的语句")
        logger.info("- Sync消息: 事务同步点")
        logger.info("- 完全避免Simple Query协议")

    finally:
        capture.cleanup()

if __name__ == "__main__":
    main()
