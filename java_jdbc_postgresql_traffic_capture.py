#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
Java JDBC PostgreSQL 流量抓包测试脚本 (psycopg3 Extended Query协议版本)
使用psycopg3的prepare=True参数强制触发Extended Query协议

Extended Query协议强制策略:
1. 使用psycopg3替代psycopg2
2. prepare=True - 强制所有查询使用Extended Query协议  
3. prepare=False - 对比测试，强制Simple Query协议
4. 预编译语句自动重用，提高性能
5. 完全控制PostgreSQL协议类型

关键特性：
- prepare=True: 强制Parse/Bind/Execute序列
- prepare=False: 强制Simple Query消息
- 自动预编译语句重用
- 精确的协议控制
"""
import psycopg
from psycopg import sql
import paramiko
import time
import os
from datetime import datetime, date, timedelta
import sys
from pathlib import Path
import subprocess
import logging
import json
import uuid
import decimal
from typing import List, Dict, Any
import threading
import random

# 配置日志
logging.basicConfig(
    level=logging.INFO,
    format='%(asctime)s - %(levelname)s - %(message)s'
)
logger = logging.getLogger(__name__)

# 数据库连接配置 - psycopg3格式 (禁用SSL以便抓包分析)
DB_CONFIG = {
    'host': '**************',
    'port': 5432,
    'dbname': 'postgres',  # psycopg3使用dbname而不是database
    'user': 'postgres',
    'password': 'mysecretpassword',
    'sslmode': 'disable'  # 🔥 禁用SSL，使SQL查询明文传输，便于协议分析
}

# 抓包配置
CAPTURE_CONFIG = {
    'host': '**************',     # 远程服务器IP
    'username': 'root',           # SSH用户名
    'password': 'root@123',       # SSH密码
    'port': 22,                   # SSH端口
    'remote_capture_dir': '/tmp/jdbc_postgresql_pcap',
    'local_dir': './java_jdbc_postgresql_pcap',
    'interface': 'eth0',          # 网络接口
    'capture_port': 5432          # PostgreSQL端口
}

class JDBCPostgreSQLTrafficCapture:
    """Java JDBC PostgreSQL 流量抓包测试类 - psycopg3 Extended Query协议版本"""
    
    def __init__(self):
        self.ssh_client = None
        self.test_results = []
        self.prepared_statements = {}
        
    def setup_ssh_connection(self):
        """建立SSH连接"""
        try:
            self.ssh_client = paramiko.SSHClient()
            self.ssh_client.set_missing_host_key_policy(paramiko.AutoAddPolicy())
            self.ssh_client.connect(
                hostname=CAPTURE_CONFIG['host'],
                port=CAPTURE_CONFIG['port'],
                username=CAPTURE_CONFIG['username'],
                password=CAPTURE_CONFIG['password']
            )
            logger.info("SSH连接建立成功")
            
            # 创建远程抓包目录
            stdin, stdout, stderr = self.ssh_client.exec_command(f"mkdir -p {CAPTURE_CONFIG['remote_capture_dir']}")
            stdout.read()
            
        except Exception as e:
            logger.error(f"SSH连接失败: {e}")
            raise
    
    def start_packet_capture(self, test_name: str) -> str:
        """开始抓包"""
        timestamp = datetime.now().strftime('%Y%m%d_%H%M%S')
        filename = f"jdbc_{test_name}_{timestamp}.pcap"
        remote_file = f"{CAPTURE_CONFIG['remote_capture_dir']}/{filename}"
        
        # 构造tcpdump命令 - 只抓取PostgreSQL端口的流量
        tcpdump_cmd = (
            f"nohup tcpdump -i {CAPTURE_CONFIG['interface']} "
            f"-w {remote_file} "
            f"port {CAPTURE_CONFIG['capture_port']} "
            f"> /dev/null 2>&1 & echo $!"
        )
        
        try:
            stdin, stdout, stderr = self.ssh_client.exec_command(tcpdump_cmd)
            pid = stdout.read().decode().strip()
            logger.info(f"开始抓包: {filename}, PID: {pid}")
            time.sleep(2)  # 增加等待时间，确保tcpdump完全启动
            return filename, pid
        except Exception as e:
            logger.error(f"启动抓包失败: {e}")
            raise
    
    def stop_packet_capture(self, pid: str, filename: str):
        """停止抓包并下载文件"""
        try:
            # 等待SQL执行完成
            time.sleep(2)  # 🔥 确保所有SQL执行完成
            
            # 停止tcpdump进程
            self.ssh_client.exec_command(f"kill {pid}")
            time.sleep(2)  # 增加等待时间，确保进程完全结束
            
            # 确保本地目录存在
            Path(CAPTURE_CONFIG['local_dir']).mkdir(parents=True, exist_ok=True)
            
            # 下载抓包文件
            remote_file = f"{CAPTURE_CONFIG['remote_capture_dir']}/{filename}"
            local_file = f"{CAPTURE_CONFIG['local_dir']}/{filename}"
            
            with self.ssh_client.open_sftp() as sftp:
                sftp.get(remote_file, local_file)
            
            logger.info(f"抓包完成，文件已下载到: {local_file}")
            
        except Exception as e:
            logger.error(f"停止抓包失败: {e}")
    
    def setup_test_environment(self):
        """设置测试环境 - 使用psycopg3创建表"""
        logger.info("设置测试环境 (psycopg3)...")
        
        try:
            with psycopg.connect(**DB_CONFIG) as conn:
                conn.autocommit = True  # psycopg3自动提交方式
                
                with conn.cursor() as cur:
                    # 预创建所有测试表
                    test_tables = [
                        """
                        CREATE TABLE IF NOT EXISTS jdbc_prepared_test (
                            id SERIAL PRIMARY KEY,
                            name VARCHAR(100),
                            age INTEGER,
                            salary DECIMAL(10,2),
                            created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP
                        )
                        """,
                        """
                        CREATE TABLE IF NOT EXISTS jdbc_transaction_test (
                            id SERIAL PRIMARY KEY,
                            operation VARCHAR(50),
                            amount DECIMAL(15,2),
                            timestamp TIMESTAMP DEFAULT CURRENT_TIMESTAMP
                        )
                        """,
                        """
                        CREATE TABLE IF NOT EXISTS jdbc_batch_test (
                            id SERIAL PRIMARY KEY,
                            category VARCHAR(50),
                            value INTEGER,
                            description TEXT
                        )
                        """,
                        """
                        CREATE TABLE IF NOT EXISTS jdbc_resultset_test (
                            id SERIAL PRIMARY KEY,
                            name VARCHAR(100),
                            department VARCHAR(50),
                            salary DECIMAL(10,2),
                            hire_date DATE
                        )
                        """,
                        """
                        CREATE TABLE IF NOT EXISTS jdbc_performance_test (
                            id SERIAL PRIMARY KEY,
                            data TEXT,
                            number INTEGER,
                            timestamp TIMESTAMP DEFAULT CURRENT_TIMESTAMP
                        )
                        """
                    ]
                    
                    for table_sql in test_tables:
                        cur.execute(table_sql, prepare=False)  # DDL使用Simple Query
                    
                    logger.info("测试环境设置完成")
            
        except Exception as e:
            logger.error(f"设置测试环境失败: {e}")
            raise
    
    def run_test_with_capture(self, test_name: str, test_func, *args, **kwargs):
        """运行测试并进行抓包"""
        try:
            # 开始抓包
            filename, pid = self.start_packet_capture(test_name)
            
            # 运行测试
            start_time = time.time()
            result = test_func(*args, **kwargs)
            end_time = time.time()
            
            # 停止抓包
            self.stop_packet_capture(pid, filename)
            
            # 记录测试结果
            self.test_results.append({
                'test_name': test_name,
                'status': 'SUCCESS',
                'duration': end_time - start_time,
                'capture_file': filename,
                'operations': result if isinstance(result, list) else []
            })
            
        except Exception as e:
            logger.error(f"测试 {test_name} 失败: {e}")
            self.test_results.append({
                'test_name': test_name,
                'status': 'FAILED',
                'error': str(e),
                'duration': 0,
                'capture_file': None,
                'operations': []
            })
    
    def test_extended_query_patterns(self):
        """测试强制Extended Query协议模式 (prepare=True)"""
        operations = []
        
        try:
            with psycopg.connect(**DB_CONFIG) as conn:
                with conn.cursor() as cur:
                    
                    # 清空表数据
                    cur.execute("DELETE FROM jdbc_prepared_test", prepare=False)
                    
                    # 1. 强制Extended Query INSERT - 应该看到Parse/Bind/Execute
                    operations.append("🔥 强制Extended Query INSERT")
                    for i in range(5):
                        cur.execute(
                            "INSERT INTO jdbc_prepared_test (name, age, salary) VALUES (%s, %s, %s)",
                            (f"ExtendedUser_{i}", 25 + i, 50000.50 + i * 1000),
                            prepare=True  # 🔥 强制Extended Query协议
                        )
                    
                    # 2. 强制Extended Query SELECT
                    operations.append("🔥 强制Extended Query SELECT")
                    cur.execute(
                        "SELECT id, name, age FROM jdbc_prepared_test WHERE age > %s AND salary < %s ORDER BY id",
                        (25, 60000),
                        prepare=True  # 🔥 强制Extended Query协议
                    )
                    results = cur.fetchall()
                    logger.info(f"Extended Query结果: {len(results)} 条记录")
                    
                    # 3. 强制Extended Query 复杂JOIN查询
                    operations.append("🔥 强制Extended Query 复杂JOIN")
                    cur.execute("""
                        SELECT p.name, p.age, p.salary 
                        FROM jdbc_prepared_test p 
                        WHERE p.id IN (
                            SELECT DISTINCT id FROM jdbc_prepared_test 
                            WHERE age BETWEEN %s AND %s
                        ) AND p.salary > %s
                        ORDER BY p.salary DESC LIMIT %s
                    """, (20, 35, 45000, 10), prepare=True)  # 🔥 强制Extended Query协议
                    results = cur.fetchall()
                    
                    # 4. 强制Extended Query 聚合查询
                    operations.append("🔥 强制Extended Query 聚合查询")
                    cur.execute("""
                        SELECT 
                            COUNT(*) as total_count,
                            AVG(age) as avg_age,
                            SUM(salary) as total_salary,
                            MAX(created_at) as latest_record
                        FROM jdbc_prepared_test 
                        WHERE age > %s
                        GROUP BY (age / %s)
                        HAVING COUNT(*) > %s
                    """, (20, 5, 0), prepare=True)  # 🔥 强制Extended Query协议
                    results = cur.fetchall()
                    
                    # 5. 强制Extended Query UPDATE
                    operations.append("🔥 强制Extended Query UPDATE")
                    cur.execute(
                        "UPDATE jdbc_prepared_test SET salary = salary * %s WHERE age < %s",
                        (1.1, 30),
                        prepare=True  # 🔥 强制Extended Query协议
                    )
                    logger.info(f"Extended Query更新: {cur.rowcount} 条记录")
                    
                    # 6. 重复查询测试预编译语句重用
                    operations.append("🔥 测试预编译语句重用")
                    for age_threshold in [25, 26, 27, 28, 29]:
                        cur.execute(
                            "SELECT COUNT(*) FROM jdbc_prepared_test WHERE age > %s",
                            (age_threshold,),
                            prepare=True  # 🔥 应该重用相同的预编译语句
                        )
                        count = cur.fetchone()[0]
                        logger.info(f"年龄>{age_threshold}: {count} 条记录")
                    
                    conn.commit()
            
        except Exception as e:
            logger.error(f"Extended Query模式测试失败: {e}")
            raise
        
        return operations
    
    def test_prepare_true_vs_false_comparison(self):
        """测试prepare=True vs prepare=False协议对比"""
        operations = []
        
        try:
            with psycopg.connect(**DB_CONFIG) as conn:
                with conn.cursor() as cur:
                    
                    # 清空表数据
                    cur.execute("DELETE FROM jdbc_transaction_test", prepare=False)
                    
                    # === 第一部分：prepare=True (Extended Query) ===
                    operations.append("🔥 Phase 1: prepare=True (Extended Query)")
                    
                    # INSERT with prepare=True
                    for i in range(5):
                        cur.execute(
                            "INSERT INTO jdbc_transaction_test (operation, amount) VALUES (%s, %s)",
                            (f"Extended_Op_{i}", 1000.00 + i * 100),
                            prepare=True  # 🔥 Extended Query协议
                        )
                    
                    # SELECT with prepare=True
                    cur.execute(
                        "SELECT operation, amount FROM jdbc_transaction_test WHERE amount > %s ORDER BY amount",
                        (1200.00,),
                        prepare=True  # 🔥 Extended Query协议
                    )
                    extended_results = cur.fetchall()
                    logger.info(f"Extended Query结果: {len(extended_results)} 条")
                    
                    # === 第二部分：prepare=False (Simple Query) ===
                    operations.append("🔥 Phase 2: prepare=False (Simple Query)")
                    
                    # INSERT with prepare=False
                    for i in range(5):
                        cur.execute(
                            "INSERT INTO jdbc_transaction_test (operation, amount) VALUES (%s, %s)",
                            (f"Simple_Op_{i}", 2000.00 + i * 100),
                            prepare=False  # 🔥 Simple Query协议
                        )
                    
                    # SELECT with prepare=False
                    cur.execute(
                        "SELECT operation, amount FROM jdbc_transaction_test WHERE amount > %s ORDER BY amount",
                        (2200.00,),
                        prepare=False  # 🔥 Simple Query协议
                    )
                    simple_results = cur.fetchall()
                    logger.info(f"Simple Query结果: {len(simple_results)} 条")
                    
                    # === 第三部分：相同查询的协议对比 ===
                    operations.append("🔥 Phase 3: 相同查询协议对比")
                    
                    test_query = "SELECT COUNT(*), AVG(amount) FROM jdbc_transaction_test WHERE amount BETWEEN %s AND %s"
                    test_params = (1000, 3000)
                    
                    # Extended Query版本
                    cur.execute(test_query, test_params, prepare=True)
                    extended_stats = cur.fetchone()
                    logger.info(f"Extended Query统计: {extended_stats}")
                    
                    # Simple Query版本  
                    cur.execute(test_query, test_params, prepare=False)
                    simple_stats = cur.fetchone()
                    logger.info(f"Simple Query统计: {simple_stats}")
                    
                    # === 第四部分：批量操作对比 ===
                    operations.append("🔥 Phase 4: 批量操作协议对比")
                    
                    # 批量Extended Query - 预编译语句重用
                    for amount in [5000, 5100, 5200, 5300, 5400]:
                        cur.execute(
                            "INSERT INTO jdbc_transaction_test (operation, amount) VALUES (%s, %s)",
                            (f"Batch_Extended", amount),
                            prepare=True  # 🔥 应该重用预编译语句
                        )
                    
                    # 批量Simple Query - 每次独立查询
                    for amount in [6000, 6100, 6200, 6300, 6400]:
                        cur.execute(
                            "INSERT INTO jdbc_transaction_test (operation, amount) VALUES (%s, %s)",
                            (f"Batch_Simple", amount),
                            prepare=False  # 🔥 每次都是Simple Query
                        )
                    
                    conn.commit()
                    
        except Exception as e:
            logger.error(f"协议对比测试失败: {e}")
            raise
        
        return operations
    
    def test_batch_extended_query_operations(self):
        """测试批量Extended Query操作 - 预编译语句重用"""
        operations = []
        
        try:
            with psycopg.connect(**DB_CONFIG) as conn:
                with conn.cursor() as cur:
                    
                    # 清空表数据
                    cur.execute("DELETE FROM jdbc_batch_test", prepare=False)
                    
                    # 批量INSERT - Extended Query预编译语句重用
                    operations.append("🔥 批量Extended Query INSERT (预编译语句重用)")
                    batch_data = [
                        ("ExtendedCategory", 100 + i, f"Extended description {i}")
                        for i in range(15)
                    ]
                    
                    for category, value, description in batch_data:
                        cur.execute(
                            "INSERT INTO jdbc_batch_test (category, value, description) VALUES (%s, %s, %s)",
                            (category, value, description),
                            prepare=True  # 🔥 第一次Parse，后续重用Bind/Execute
                        )
                    
                    # 批量UPDATE - Extended Query预编译语句重用
                    operations.append("🔥 批量Extended Query UPDATE (预编译语句重用)")
                    for i in range(10):
                        cur.execute(
                            "UPDATE jdbc_batch_test SET value = value + %s WHERE category = %s",
                            (i * 5, "ExtendedCategory"),
                            prepare=True  # 🔥 重用相同预编译语句
                        )
                    
                    # 批量SELECT - 测试不同参数的预编译语句重用
                    operations.append("🔥 批量Extended Query SELECT (参数变化)")
                    for min_value in [100, 110, 120, 130, 140]:
                        cur.execute(
                            "SELECT category, COUNT(*), AVG(value) FROM jdbc_batch_test WHERE value > %s GROUP BY category",
                            (min_value,),
                            prepare=True  # 🔥 重用相同SQL模板
                        )
                        results = cur.fetchall()
                        logger.info(f"Extended Query批量查询 (value>{min_value}): {len(results)} 组")
                    
                    # 复杂聚合查询 - Extended Query
                    operations.append("🔥 Extended Query 复杂聚合查询")
                    cur.execute("""
                        SELECT 
                            category, 
                            COUNT(*) as item_count,
                            AVG(value) as avg_value,
                            MIN(value) as min_value,
                            MAX(value) as max_value,
                            STRING_AGG(description, ' | ') as descriptions
                        FROM jdbc_batch_test 
                        WHERE category LIKE %s AND value > %s
                        GROUP BY category 
                        HAVING COUNT(*) > %s
                        ORDER BY avg_value DESC
                    """, ("Extended%", 100, 5), prepare=True)  # 🔥 Extended Query协议
                    
                    results = cur.fetchall()
                    logger.info(f"Extended Query聚合结果: {len(results)} 个分组")
                    
                    # DELETE操作 - Extended Query
                    operations.append("🔥 Extended Query DELETE")
                    cur.execute(
                        "DELETE FROM jdbc_batch_test WHERE value < %s AND category = %s",
                        (105, "ExtendedCategory"),
                        prepare=True  # 🔥 Extended Query协议
                    )
                    logger.info(f"Extended Query删除: {cur.rowcount} 条记录")
                    
                    conn.commit()
            
        except Exception as e:
            logger.error(f"批量Extended Query操作测试失败: {e}")
            raise
        
        return operations
    
    def test_complex_extended_queries(self):
        """测试复杂Extended Query查询模式"""
        operations = []
        
        try:
            with psycopg.connect(**DB_CONFIG) as conn:
                with conn.cursor() as cur:
                    
                    # 清空并插入测试数据
                    cur.execute("DELETE FROM jdbc_performance_test", prepare=False)
                    
                    operations.append("🔥 插入复杂查询测试数据 (Extended Query)")
                    for i in range(25):
                        cur.execute(
                            "INSERT INTO jdbc_performance_test (data, number) VALUES (%s, %s)",
                            (f"ExtendedData_{i}_{'X' * (i % 10)}", random.randint(1, 1000)),
                            prepare=True  # 🔥 Extended Query协议
                        )
                    
                    # 1. Extended Query窗口函数查询
                    operations.append("🔥 Extended Query 窗口函数查询")
                    cur.execute("""
                        SELECT 
                            id, 
                            data,
                            number,
                            ROW_NUMBER() OVER (ORDER BY number DESC) as rank,
                            LAG(number, 1) OVER (ORDER BY id) as prev_number,
                            LEAD(number, 1) OVER (ORDER BY id) as next_number
                        FROM jdbc_performance_test 
                        WHERE number > %s AND LENGTH(data) > %s
                        ORDER BY number DESC 
                        LIMIT %s
                    """, (100, 20, 15), prepare=True)  # 🔥 Extended Query协议
                    results = cur.fetchall()
                    logger.info(f"Extended Query窗口函数结果: {len(results)} 条")
                    
                    # 2. Extended Query CTE查询
                    operations.append("🔥 Extended Query CTE（公用表表达式）")
                    cur.execute("""
                        WITH ranked_data AS (
                            SELECT id, data, number,
                                   DENSE_RANK() OVER (ORDER BY number) as rank,
                                   NTILE(4) OVER (ORDER BY number) as quartile
                            FROM jdbc_performance_test
                            WHERE number BETWEEN %s AND %s
                        ),
                        stats AS (
                            SELECT 
                                AVG(number) as avg_num, 
                                COUNT(*) as total_count,
                                STDDEV(number) as std_dev
                            FROM ranked_data
                        )
                        SELECT r.*, s.avg_num, s.total_count, s.std_dev
                        FROM ranked_data r
                        CROSS JOIN stats s
                        WHERE r.rank <= %s AND r.quartile = %s
                        ORDER BY r.number DESC
                    """, (200, 800, 8, 4), prepare=True)  # 🔥 Extended Query协议
                    results = cur.fetchall()
                    logger.info(f"Extended Query CTE结果: {len(results)} 条")
                    
                    # 3. Extended Query 复杂子查询
                    operations.append("🔥 Extended Query 复杂子查询")
                    cur.execute("""
                        SELECT p1.id, p1.data, p1.number,
                               (SELECT COUNT(*) FROM jdbc_performance_test p3 WHERE p3.number < p1.number) as lower_count
                        FROM jdbc_performance_test p1
                        WHERE p1.number > (
                            SELECT AVG(p2.number) 
                            FROM jdbc_performance_test p2 
                            WHERE p2.id < p1.id AND p2.number > %s
                        ) AND p1.id IN (
                            SELECT id FROM jdbc_performance_test 
                            WHERE LENGTH(data) > %s AND number > %s
                            ORDER BY number DESC 
                            LIMIT %s
                        )
                        ORDER BY p1.number DESC
                    """, (50, 25, 100, 12), prepare=True)  # 🔥 Extended Query协议
                    results = cur.fetchall()
                    logger.info(f"Extended Query子查询结果: {len(results)} 条")
                    
                    # 4. Extended Query 聚合分析查询
                    operations.append("🔥 Extended Query 聚合分析查询")
                    cur.execute("""
                        SELECT 
                            CASE 
                                WHEN number < %s THEN 'Low'
                                WHEN number < %s THEN 'Medium' 
                                ELSE 'High'
                            END as number_category,
                            COUNT(*) as count,
                            AVG(number) as avg_number,
                            MIN(number) as min_number,
                            MAX(number) as max_number,
                            SUM(LENGTH(data)) as total_data_length,
                            ARRAY_AGG(id ORDER BY number DESC) as sorted_ids
                        FROM jdbc_performance_test
                        WHERE number > %s
                        GROUP BY 
                            CASE 
                                WHEN number < %s THEN 'Low'
                                WHEN number < %s THEN 'Medium' 
                                ELSE 'High'
                            END
                        HAVING COUNT(*) > %s
                        ORDER BY avg_number DESC
                    """, (300, 700, 50, 300, 700, 1), prepare=True)  # 🔥 Extended Query协议
                    results = cur.fetchall()
                    logger.info(f"Extended Query聚合分析结果: {len(results)} 组")
                    
                    # 5. Extended Query UPDATE with 复杂WHERE子句
                    operations.append("🔥 Extended Query 复杂UPDATE")
                    cur.execute("""
                        UPDATE jdbc_performance_test 
                        SET number = number * %s
                        WHERE id IN (
                            SELECT id FROM jdbc_performance_test 
                            WHERE number > (SELECT AVG(number) FROM jdbc_performance_test)
                            AND LENGTH(data) > %s
                            ORDER BY number ASC
                            LIMIT %s
                        )
                    """, (1.1, 20, 5), prepare=True)  # 🔥 Extended Query协议
                    logger.info(f"Extended Query复杂UPDATE: {cur.rowcount} 条记录")
                    
                    conn.commit()
            
        except Exception as e:
            logger.error(f"复杂Extended Query测试失败: {e}")
            raise
        
        return operations
    
    def run_all_tests(self):
        """运行所有测试"""
        logger.info("🔥 开始psycopg3 Extended Query PostgreSQL流量抓包测试")
        
        # 设置SSH连接
        self.setup_ssh_connection()
        
        # 设置测试环境
        self.setup_test_environment()
        
        # 运行各种测试
        test_cases = [
            ("强制Extended_Query模式", self.test_extended_query_patterns),
            ("prepare_True_vs_False对比", self.test_prepare_true_vs_false_comparison),
            ("批量Extended_Query操作", self.test_batch_extended_query_operations),
            ("复杂Extended_Query查询", self.test_complex_extended_queries),
        ]
        
        for test_name, test_func in test_cases:
            logger.info(f"🔥 运行测试: {test_name}")
            self.run_test_with_capture(test_name, test_func)
            time.sleep(3)  # 增加测试间隔，确保抓包完整
        
        # 生成报告
        self.generate_report()
        
        # 清理
        if self.ssh_client:
            self.ssh_client.close()
    
    def generate_report(self):
        """生成测试报告"""
        timestamp = datetime.now().strftime('%Y%m%d_%H%M%S')
        report_file = f"{CAPTURE_CONFIG['local_dir']}/test_report_{timestamp}.md"
        
        # 确保目录存在
        Path(CAPTURE_CONFIG['local_dir']).mkdir(parents=True, exist_ok=True)
        
        report_content = f"""# psycopg3 Extended Query PostgreSQL 流量抓包测试报告

## 测试概要
- 测试时间: {datetime.now().strftime('%Y-%m-%d %H:%M:%S')}
- 测试用例总数: {len(self.test_results)}
- 成功测试: {len([r for r in self.test_results if r['status'] == 'SUCCESS'])}
- 失败测试: {len([r for r in self.test_results if r['status'] == 'FAILED'])}
- **使用驱动**: psycopg3 (支持强制Extended Query协议)

## 🔥 psycopg3 Extended Query协议特性
本版本使用psycopg3的prepare=True参数强制触发Extended Query协议：

1. **prepare=True**: 🔥 强制所有查询使用Extended Query协议 (Parse/Bind/Execute)
2. **prepare=False**: 🔥 强制所有查询使用Simple Query协议  
3. **预编译语句重用**: 相同SQL模板自动重用，只需要Bind/Execute
4. **完全协议控制**: 彻底解决psycopg2的协议选择限制
5. **性能优化**: 重复查询避免重复Parse，显著提升性能

## 协议分析指导
使用Wireshark分析PCAP文件时，应该能清楚看到两种协议：

### 过滤器设置
```bash
# 只显示PostgreSQL协议消息
pgsql

# 只显示Extended Query消息 (prepare=True的结果)
pgsql.type == "Parse" or pgsql.type == "Bind" or pgsql.type == "Execute" or pgsql.type == "Sync"

# 只显示Simple Query消息 (prepare=False的结果)
pgsql.type == "Simple query"

# 查看预编译语句重用
pgsql.type == "Bind" or pgsql.type == "Execute"
```

### 🔥 预期结果 (本次应该100%成功)
- **prepare=True查询**: 必须看到Parse→Bind→Execute→Sync序列
- **prepare=False查询**: 必须看到Simple Query消息
- **重复查询**: 第一次Parse，后续只有Bind→Execute→Sync

### 🔥 关键观察点
1. **Parse消息**: SQL模板中参数显示为$1, $2, $3...
2. **Bind消息**: 实际参数值绑定到预编译语句
3. **Execute消息**: 执行已绑定的预编译语句
4. **Sync消息**: Extended Query事务同步
5. **重用模式**: 相同SQL只Parse一次，后续重用

## 测试结果详情

"""
        
        for result in self.test_results:
            status_icon = "✅" if result['status'] == 'SUCCESS' else "❌"
            report_content += f"""### {status_icon} {result['test_name']}
- 状态: {result['status']}
- 耗时: {result['duration']:.2f}秒
- 抓包文件: {result.get('capture_file', 'N/A')}
"""
            if result['status'] == 'FAILED':
                report_content += f"- 错误信息: {result.get('error', 'Unknown error')}\n"
            
            if result.get('operations'):
                report_content += "- 执行的操作:\n"
                for op in result['operations']:
                    report_content += f"  - {op}\n"
            
            report_content += "\n"
        
        report_content += f"""
## 抓包文件说明
所有PCAP文件保存在: {CAPTURE_CONFIG['local_dir']}

### 🔥 psycopg3优势确认
**完全协议控制**: 本次测试使用psycopg3，应该能100%控制协议类型！

**预期成果**:
1. ✅ **prepare=True查询**: 全部显示Extended Query协议 (Parse/Bind/Execute/Sync)
2. ✅ **prepare=False查询**: 全部显示Simple Query协议
3. ✅ **预编译语句重用**: 相同SQL只Parse一次，性能优化明显
4. ✅ **协议切换**: 同一脚本中可以随意切换协议类型

### 🔥 与psycopg2对比
- **psycopg2**: 无法控制协议，主要使用Simple Query
- **psycopg3**: 完全控制协议，prepare=True/False精确控制
- **JDBC等价**: psycopg3的prepare=True ≈ JDBC的preferQueryMode=extended

### 📊 性能分析重点
1. **Extended Query优势**: 预编译语句重用，减少Parse开销
2. **Simple Query适用**: 一次性查询，DDL语句
3. **网络效率**: Extended Query参数传输更高效
4. **批量操作**: Extended Query显著减少网络往返

### 🔧 分析工具
使用以下命令快速分析PCAP文件：
```bash
# 统计协议消息类型
tshark -r filename.pcap -Y "pgsql" -T fields -e pgsql.type | sort | uniq -c

# 查看Extended Query序列
tshark -r filename.pcap -Y "pgsql.type == Parse or pgsql.type == Bind or pgsql.type == Execute"

# 查看Simple Query
tshark -r filename.pcap -Y "pgsql.type == 'Simple query'"
```

**🎯 成功指标**: Extended Query消息数量应该远大于Simple Query消息数量

生成时间: {datetime.now().strftime('%Y-%m-%d %H:%M:%S')}
"""
        
        # 写入报告文件
        with open(report_file, 'w', encoding='utf-8') as f:
            f.write(report_content)
        
        logger.info(f"测试报告已生成: {report_file}")
        
        # 打印总结
        success_count = len([r for r in self.test_results if r['status'] == 'SUCCESS'])
        total_count = len(self.test_results)
        logger.info(f"测试完成! 成功率: {success_count}/{total_count} ({success_count/total_count*100:.1f}%)")

if __name__ == "__main__":
    capturer = JDBCPostgreSQLTrafficCapture()
    capturer.run_all_tests()
