#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
PostgreSQL全面流量抓包脚本
基于PostgreSQL官方文档创建的全面测试用例
涵盖所有主要的SQL命令、数据类型、函数和操作符
"""
import psycopg2
import paramiko
import time
import os
from datetime import datetime, date, timedelta
import sys
from pathlib import Path
import subprocess
import logging
import json
import uuid
import decimal
from typing import List, Dict, Any

# 配置日志
logging.basicConfig(
    level=logging.INFO,
    format='%(asctime)s - %(levelname)s - %(message)s'
)
logger = logging.getLogger(__name__)

# 服务器配置
SERVER_CONFIG = {
    'hostname': '**************',
    'username': 'root',
    'password': 'root@123',
    'port': 22
}

# 数据库配置
DB_CONFIG = {
    'host': '**************',
    'port': 5432,
    'database': 'postgres',
    'user': 'postgres',
    'password': 'mysecretpassword'
}

# 抓包配置
CAPTURE_CONFIG = {
    'interface': 'eth0',
    'port': 5432,
    'remote_dir': '/root/pcap',
    'local_dir': './postgres_comprehensive_pcap'
}

class ComprehensiveTrafficCapture:
    def __init__(self):
        self.ssh = None
        self.sftp = None
        self.connect_ssh()
        self.setup_directories()
        self.test_results = []
        
    def connect_ssh(self):
        """连接SSH服务器"""
        try:
            self.ssh = paramiko.SSHClient()
            self.ssh.set_missing_host_key_policy(paramiko.AutoAddPolicy())
            self.ssh.connect(**SERVER_CONFIG)
            self.sftp = self.ssh.open_sftp()
            logger.info("SSH连接成功")
        except Exception as e:
            logger.error(f"SSH连接失败: {e}")
            sys.exit(1)

    def setup_directories(self):
        """设置远程和本地目录"""
        self.ssh.exec_command(f"mkdir -p {CAPTURE_CONFIG['remote_dir']}")
        Path(CAPTURE_CONFIG['local_dir']).mkdir(parents=True, exist_ok=True)

    def start_capture(self, capture_type):
        """开始抓包"""
        timestamp = datetime.now().strftime('%Y%m%d_%H%M%S')
        remote_file = f"{CAPTURE_CONFIG['remote_dir']}/pg_{capture_type}_{timestamp}.pcap"
        cmd = f"tcpdump -i {CAPTURE_CONFIG['interface']} -s 0 -w {remote_file} port {CAPTURE_CONFIG['port']}"
        logger.info(f"开始抓包: {capture_type}")
        self.ssh.exec_command(f"nohup {cmd} > /dev/null 2>&1 &")
        time.sleep(2)
        return remote_file

    def stop_capture(self):
        """停止抓包"""
        logger.info("停止抓包...")
        self.ssh.exec_command("pkill tcpdump")
        time.sleep(2)

    def download_pcap(self, remote_file):
        """下载pcap文件到本地"""
        filename = os.path.basename(remote_file)
        local_file = os.path.join(CAPTURE_CONFIG['local_dir'], filename)
        self.sftp.get(remote_file, local_file)
        logger.info(f"已下载: {filename}")
        return local_file

    def close_connections_and_stop_capture(self, *connections):
        """关闭数据库连接并停止抓包"""
        for conn_obj in connections:
            if conn_obj and hasattr(conn_obj, 'close'):
                try:
                    conn_obj.close()
                except:
                    pass
        time.sleep(3)  # 等待抓包完成
        self.stop_capture()

    def execute_test_with_capture(self, test_name: str, test_func):
        """执行测试并抓包"""
        logger.info(f"\n=== 开始 {test_name} 测试 ===")
        remote_file = self.start_capture(test_name.lower().replace(' ', '_'))
        
        try:
            operations = test_func()
            # 注意：test_func()内部应该在关闭数据库连接后再调用stop_capture()
            # 这里不需要再调用stop_capture()，因为它应该在test_func()内部调用
            local_file = self.download_pcap(remote_file)
            
            self.test_results.append({
                'test_name': test_name,
                'pcap_file': local_file,
                'operations': operations,
                'status': 'SUCCESS',
                'timestamp': datetime.now().isoformat()
            })
            logger.info(f"{test_name} 测试完成")
            
        except Exception as e:
            logger.error(f"{test_name} 测试失败: {e}")
            self.stop_capture()
            try:
                local_file = self.download_pcap(remote_file)
                self.test_results.append({
                    'test_name': test_name,
                    'pcap_file': local_file,
                    'operations': [],
                    'status': 'FAILED',
                    'error': str(e),
                    'timestamp': datetime.now().isoformat()
                })
            except:
                pass

    def test_ddl_operations(self):
        """测试DDL操作"""
        conn = psycopg2.connect(**DB_CONFIG)
        cur = conn.cursor()
        operations = []
        
        try:
            # 创建模式
            operations.append("CREATE SCHEMA test_schema")
            cur.execute(operations[-1])
            
            # 创建域
            operations.append("CREATE DOMAIN test_domain AS INTEGER CHECK (VALUE > 0)")
            cur.execute(operations[-1])
            
            # 创建枚举类型
            operations.append("CREATE TYPE mood AS ENUM ('sad', 'ok', 'happy')")
            cur.execute(operations[-1])
            
            # 创建复合类型
            operations.append("CREATE TYPE complex AS (r double precision, i double precision)")
            cur.execute(operations[-1])
            
            # 创建表
            operations.append("""
                CREATE TABLE test_schema.comprehensive_test (
                    id SERIAL PRIMARY KEY,
                    name VARCHAR(100) NOT NULL,
                    age test_domain,
                    mood_state mood,
                    complex_num complex,
                    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP
                )
            """)
            cur.execute(operations[-1])
            
            # 创建索引
            operations.append("CREATE INDEX idx_name ON test_schema.comprehensive_test(name)")
            cur.execute(operations[-1])
            
            # 创建视图
            operations.append("""
                CREATE VIEW test_schema.test_view AS 
                SELECT id, name, age FROM test_schema.comprehensive_test WHERE age > 18
            """)
            cur.execute(operations[-1])
            
            # 修改表结构
            operations.append("ALTER TABLE test_schema.comprehensive_test ADD COLUMN email VARCHAR(255)")
            cur.execute(operations[-1])
            
            conn.commit()

            # 清理
            try:
                cur.execute("DROP SCHEMA test_schema CASCADE")
                cur.execute("DROP TYPE mood CASCADE")
                cur.execute("DROP TYPE complex CASCADE")
                cur.execute("DROP DOMAIN test_domain CASCADE")
                conn.commit()
            except:
                pass

            cur.close()
            conn.close()

            # 在关闭数据库连接后停止抓包
            time.sleep(3)
            self.stop_capture()

            return operations

        except Exception as e:
            # 发生异常时也要关闭连接和停止抓包
            try:
                if 'cur' in locals():
                    cur.close()
                if 'conn' in locals():
                    conn.close()
                time.sleep(3)
                self.stop_capture()
            except:
                pass
            raise e

    def test_data_types(self):
        """测试各种数据类型"""
        conn = psycopg2.connect(**DB_CONFIG)
        cur = conn.cursor()
        operations = []
        
        try:
            # 创建包含所有数据类型的表
            operations.append("""
                CREATE TABLE data_types_test (
                    -- 数值类型
                    col_smallint SMALLINT,
                    col_integer INTEGER,
                    col_bigint BIGINT,
                    col_decimal DECIMAL(10,2),
                    col_numeric NUMERIC(15,5),
                    col_real REAL,
                    col_double DOUBLE PRECISION,
                    col_smallserial SMALLSERIAL,
                    col_serial SERIAL,
                    col_bigserial BIGSERIAL,
                    
                    -- 字符类型
                    col_char CHAR(10),
                    col_varchar VARCHAR(100),
                    col_text TEXT,
                    
                    -- 二进制类型
                    col_bytea BYTEA,
                    
                    -- 日期时间类型
                    col_date DATE,
                    col_time TIME,
                    col_timetz TIME WITH TIME ZONE,
                    col_timestamp TIMESTAMP,
                    col_timestamptz TIMESTAMP WITH TIME ZONE,
                    col_interval INTERVAL,
                    
                    -- 布尔类型
                    col_boolean BOOLEAN,
                    
                    -- 几何类型
                    col_point POINT,
                    col_line LINE,
                    col_lseg LSEG,
                    col_box BOX,
                    col_path PATH,
                    col_polygon POLYGON,
                    col_circle CIRCLE,
                    
                    -- 网络地址类型
                    col_cidr CIDR,
                    col_inet INET,
                    col_macaddr MACADDR,
                    
                    -- 位串类型
                    col_bit BIT(8),
                    col_varbit BIT VARYING(16),
                    
                    -- UUID类型
                    col_uuid UUID,
                    
                    -- JSON类型
                    col_json JSON,
                    col_jsonb JSONB,
                    
                    -- 数组类型
                    col_int_array INTEGER[],
                    col_text_array TEXT[],
                    
                    -- 范围类型
                    col_int4range INT4RANGE,
                    col_tsrange TSRANGE
                )
            """)
            cur.execute(operations[-1])
            
            # 插入测试数据
            operations.append("""
                INSERT INTO data_types_test (
                    col_smallint, col_integer, col_bigint, col_decimal, col_numeric,
                    col_real, col_double, col_char, col_varchar, col_text,
                    col_bytea, col_date, col_time, col_timestamp, col_interval,
                    col_boolean, col_point, col_box, col_cidr, col_inet,
                    col_macaddr, col_bit, col_varbit, col_uuid, col_json, col_jsonb,
                    col_int_array, col_text_array, col_int4range
                ) VALUES (
                    32767, 2147483647, 9223372036854775807, 12345.67, 12345.67890,
                    123.45, 123456.789012, 'test      ', 'test varchar', 'test text',
                    '\\xDEADBEEF', '2023-12-25', '14:30:00', '2023-12-25 14:30:00', '1 year 2 months',
                    true, '(1,2)', '((0,0),(1,1))', '192.168.1.0/24', '192.168.1.1',
                    '08:00:2b:01:02:03', B'10101010', B'1010101011110000', 
                    'a0eebc99-9c0b-4ef8-bb6d-6bb9bd380a11',
                    '{"name": "test", "value": 123}', '{"name": "test", "value": 123}',
                    '{1,2,3,4,5}', '{"apple","banana","cherry"}', '[1,10)'
                )
            """)
            cur.execute(operations[-1])
            
            # 查询数据
            operations.append("SELECT * FROM data_types_test")
            cur.execute(operations[-1])
            result = cur.fetchone()
            logger.info(f"数据类型测试结果: {len(result)} 列数据")
            
            conn.commit()

            # 清理
            try:
                cur.execute("DROP TABLE data_types_test")
                conn.commit()
            except:
                pass

            cur.close()
            conn.close()

            # 在关闭数据库连接后停止抓包
            time.sleep(3)
            self.stop_capture()

            return operations

        except Exception as e:
            # 发生异常时也要关闭连接和停止抓包
            try:
                if 'cur' in locals():
                    cur.close()
                if 'conn' in locals():
                    conn.close()
                time.sleep(3)
                self.stop_capture()
            except:
                pass
            raise e

    def test_dml_operations(self):
        """测试DML操作"""
        conn = psycopg2.connect(**DB_CONFIG)
        cur = conn.cursor()
        operations = []
        
        try:
            # 创建测试表
            operations.append("""
                CREATE TABLE dml_test (
                    id SERIAL PRIMARY KEY,
                    name VARCHAR(100),
                    age INTEGER,
                    salary DECIMAL(10,2),
                    department VARCHAR(50),
                    hire_date DATE,
                    is_active BOOLEAN DEFAULT true
                )
            """)
            cur.execute(operations[-1])
            
            # INSERT测试
            operations.append("""
                INSERT INTO dml_test (name, age, salary, department, hire_date) VALUES
                ('Alice Johnson', 30, 75000.00, 'Engineering', '2020-01-15'),
                ('Bob Smith', 25, 65000.00, 'Marketing', '2021-03-20'),
                ('Carol Davis', 35, 85000.00, 'Engineering', '2019-07-10'),
                ('David Wilson', 28, 70000.00, 'Sales', '2020-11-05'),
                ('Eve Brown', 32, 80000.00, 'HR', '2018-09-12')
            """)
            cur.execute(operations[-1])
            
            # UPDATE测试
            operations.append("UPDATE dml_test SET salary = salary * 1.1 WHERE department = 'Engineering'")
            cur.execute(operations[-1])
            
            operations.append("UPDATE dml_test SET is_active = false WHERE age > 33")
            cur.execute(operations[-1])
            
            # DELETE测试
            operations.append("DELETE FROM dml_test WHERE is_active = false")
            cur.execute(operations[-1])
            
            # MERGE测试 (PostgreSQL 15+)
            operations.append("""
                CREATE TABLE dml_target AS SELECT * FROM dml_test WHERE false
            """)
            cur.execute(operations[-1])
            
            try:
                operations.append("""
                    MERGE INTO dml_target t
                    USING dml_test s ON t.id = s.id
                    WHEN NOT MATCHED THEN
                        INSERT (name, age, salary, department, hire_date, is_active)
                        VALUES (s.name, s.age, s.salary, s.department, s.hire_date, s.is_active)
                """)
                cur.execute(operations[-1])
            except psycopg2.Error as e:
                logger.info(f"MERGE不支持或语法错误: {e}")
            
            conn.commit()

            # 清理
            try:
                cur.execute("DROP TABLE IF EXISTS dml_test CASCADE")
                cur.execute("DROP TABLE IF EXISTS dml_target CASCADE")
                conn.commit()
            except:
                pass

        except Exception as e:
            logger.error(f"DML操作测试失败: {e}")
            conn.rollback()
            raise e
        finally:
            # 关键：先关闭数据库连接，再停止抓包
            cur.close()
            conn.close()
            logger.info("数据库连接已关闭")

            time.sleep(3)  # 等待网络包传输完成
            self.stop_capture()

        return operations

    def test_complex_queries(self):
        """测试复杂查询"""
        conn = psycopg2.connect(**DB_CONFIG)
        cur = conn.cursor()
        operations = []

        try:
            # 创建测试表
            operations.append("""
                CREATE TABLE employees (
                    id SERIAL PRIMARY KEY,
                    name VARCHAR(100),
                    department VARCHAR(50),
                    salary DECIMAL(10,2),
                    manager_id INTEGER REFERENCES employees(id),
                    hire_date DATE
                )
            """)
            cur.execute(operations[-1])

            operations.append("""
                CREATE TABLE departments (
                    id SERIAL PRIMARY KEY,
                    name VARCHAR(50),
                    budget DECIMAL(12,2)
                )
            """)
            cur.execute(operations[-1])

            # 插入测试数据
            operations.append("""
                INSERT INTO departments (name, budget) VALUES
                ('Engineering', 1000000.00),
                ('Marketing', 500000.00),
                ('Sales', 750000.00),
                ('HR', 300000.00)
            """)
            cur.execute(operations[-1])

            operations.append("""
                INSERT INTO employees (name, department, salary, manager_id, hire_date) VALUES
                ('John CEO', 'Executive', 200000.00, NULL, '2015-01-01'),
                ('Alice Manager', 'Engineering', 120000.00, 1, '2016-03-15'),
                ('Bob Developer', 'Engineering', 80000.00, 2, '2018-06-20'),
                ('Carol Developer', 'Engineering', 85000.00, 2, '2017-09-10'),
                ('David Sales Manager', 'Sales', 110000.00, 1, '2016-11-05'),
                ('Eve Sales Rep', 'Sales', 60000.00, 5, '2019-02-14')
            """)
            cur.execute(operations[-1])

            # 窗口函数测试
            operations.append("""
                SELECT
                    name,
                    department,
                    salary,
                    ROW_NUMBER() OVER (PARTITION BY department ORDER BY salary DESC) as dept_rank,
                    LAG(salary) OVER (ORDER BY hire_date) as prev_salary,
                    SUM(salary) OVER (PARTITION BY department) as dept_total_salary
                FROM employees
            """)
            cur.execute(operations[-1])
            results = cur.fetchall()
            logger.info(f"窗口函数查询返回 {len(results)} 行")

            # CTE (公共表表达式) 测试
            operations.append("""
                WITH dept_stats AS (
                    SELECT
                        department,
                        COUNT(*) as emp_count,
                        AVG(salary) as avg_salary,
                        MAX(salary) as max_salary
                    FROM employees
                    GROUP BY department
                ),
                high_salary_depts AS (
                    SELECT department FROM dept_stats WHERE avg_salary > 70000
                )
                SELECT e.name, e.department, e.salary
                FROM employees e
                JOIN high_salary_depts h ON e.department = h.department
                ORDER BY e.salary DESC
            """)
            cur.execute(operations[-1])
            results = cur.fetchall()
            logger.info(f"CTE查询返回 {len(results)} 行")

            # 递归CTE测试
            operations.append("""
                WITH RECURSIVE employee_hierarchy AS (
                    -- 基础情况：顶级管理者
                    SELECT id, name, manager_id, 0 as level, CAST(name AS TEXT) as path
                    FROM employees
                    WHERE manager_id IS NULL

                    UNION ALL

                    -- 递归情况：下级员工
                    SELECT e.id, e.name, e.manager_id, eh.level + 1,
                           CAST(eh.path || ' -> ' || e.name AS TEXT) as path
                    FROM employees e
                    JOIN employee_hierarchy eh ON e.manager_id = eh.id
                )
                SELECT id, name, manager_id, level, path FROM employee_hierarchy ORDER BY level, name
            """)
            cur.execute(operations[-1])
            results = cur.fetchall()
            logger.info(f"递归CTE查询返回 {len(results)} 行")

            # 子查询测试
            operations.append("""
                SELECT name, salary,
                    (SELECT AVG(salary) FROM employees WHERE department = e.department) as dept_avg
                FROM employees e
                WHERE salary > (SELECT AVG(salary) FROM employees)
            """)
            cur.execute(operations[-1])
            results = cur.fetchall()
            logger.info(f"子查询返回 {len(results)} 行")

            # CASE表达式测试
            operations.append("""
                SELECT name, salary,
                    CASE
                        WHEN salary > 100000 THEN 'High'
                        WHEN salary > 70000 THEN 'Medium'
                        ELSE 'Low'
                    END as salary_grade,
                    COALESCE(manager_id::text, 'No Manager') as manager_info
                FROM employees
            """)
            cur.execute(operations[-1])
            results = cur.fetchall()
            logger.info(f"CASE表达式查询返回 {len(results)} 行")

            conn.commit()

            # 清理
            try:
                cur.execute("DROP TABLE IF EXISTS employees CASCADE")
                cur.execute("DROP TABLE IF EXISTS departments CASCADE")
                conn.commit()
            except:
                pass

            cur.close()
            conn.close()

            # 在关闭数据库连接后停止抓包
            time.sleep(3)
            self.stop_capture()

            return operations

        except Exception as e:
            # 发生异常时也要关闭连接和停止抓包
            try:
                if 'cur' in locals():
                    cur.close()
                if 'conn' in locals():
                    conn.close()
                time.sleep(3)
                self.stop_capture()
            except:
                pass
            raise e

    def test_functions_and_operators(self):
        """测试函数和操作符"""
        conn = psycopg2.connect(**DB_CONFIG)
        cur = conn.cursor()
        operations = []

        try:
            # 数学函数测试
            operations.append("SELECT ABS(-42), CEIL(4.3), FLOOR(4.7), ROUND(4.567, 2), SQRT(16), POWER(2, 3)")
            cur.execute(operations[-1])
            result = cur.fetchone()
            logger.info(f"数学函数结果: {result}")

            # 字符串函数测试
            operations.append("""
                SELECT
                    LENGTH('PostgreSQL'),
                    UPPER('postgresql'),
                    LOWER('POSTGRESQL'),
                    SUBSTRING('PostgreSQL' FROM 1 FOR 8),
                    CONCAT('Hello', ' ', 'World'),
                    REPLACE('Hello World', 'World', 'PostgreSQL'),
                    TRIM('  spaces  '),
                    LEFT('PostgreSQL', 4),
                    RIGHT('PostgreSQL', 3)
            """)
            cur.execute(operations[-1])
            result = cur.fetchone()
            logger.info(f"字符串函数结果: {result}")

            # 日期时间函数测试
            operations.append("""
                SELECT
                    NOW(),
                    CURRENT_DATE,
                    CURRENT_TIME,
                    EXTRACT(YEAR FROM NOW()),
                    EXTRACT(MONTH FROM NOW()),
                    EXTRACT(DAY FROM NOW()),
                    DATE_TRUNC('month', NOW()),
                    NOW() + INTERVAL '1 day',
                    AGE(NOW(), '1990-01-01'::date)
            """)
            cur.execute(operations[-1])
            result = cur.fetchone()
            logger.info(f"日期时间函数结果: {result}")

            # 聚合函数测试
            operations.append("""
                CREATE TABLE test_aggregates (
                    category VARCHAR(20),
                    value INTEGER
                )
            """)
            cur.execute(operations[-1])

            operations.append("""
                INSERT INTO test_aggregates VALUES
                ('A', 10), ('A', 20), ('A', 30),
                ('B', 15), ('B', 25), ('B', 35),
                ('C', 5), ('C', 15), ('C', 25)
            """)
            cur.execute(operations[-1])

            operations.append("""
                SELECT
                    category,
                    COUNT(*) as count,
                    SUM(value) as sum,
                    AVG(value) as avg,
                    MIN(value) as min,
                    MAX(value) as max,
                    STDDEV(value) as stddev,
                    VARIANCE(value) as variance,
                    STRING_AGG(value::text, ', ' ORDER BY value) as values_list
                FROM test_aggregates
                GROUP BY category
                ORDER BY category
            """)
            cur.execute(operations[-1])
            results = cur.fetchall()
            logger.info(f"聚合函数查询返回 {len(results)} 行")

            # 条件表达式测试
            operations.append("""
                SELECT
                    value,
                    CASE WHEN value > 20 THEN 'High' ELSE 'Low' END as grade,
                    COALESCE(NULL, value, 0) as coalesced,
                    NULLIF(value, 15) as nullif_result,
                    GREATEST(value, 20) as greatest,
                    LEAST(value, 20) as least
                FROM test_aggregates
                LIMIT 5
            """)
            cur.execute(operations[-1])
            results = cur.fetchall()
            logger.info(f"条件表达式查询返回 {len(results)} 行")

            conn.commit()

            # 清理
            try:
                cur.execute("DROP TABLE IF EXISTS test_aggregates")
                conn.commit()
            except:
                pass

        except Exception as e:
            logger.error(f"函数和操作符测试失败: {e}")
            conn.rollback()
            raise e
        finally:
            # 关键：先关闭数据库连接，再停止抓包
            cur.close()
            conn.close()
            logger.info("数据库连接已关闭")

            time.sleep(3)  # 等待网络包传输完成
            self.stop_capture()

        return operations

    def test_json_operations(self):
        """测试JSON操作"""
        conn = psycopg2.connect(**DB_CONFIG)
        cur = conn.cursor()
        operations = []

        try:
            # 创建JSON测试表
            operations.append("""
                CREATE TABLE json_test (
                    id SERIAL PRIMARY KEY,
                    data JSON,
                    data_binary JSONB
                )
            """)
            cur.execute(operations[-1])

            # 插入JSON数据
            operations.append("""
                INSERT INTO json_test (data, data_binary) VALUES
                ('{"name": "Alice", "age": 30, "skills": ["Python", "SQL", "JavaScript"]}',
                 '{"name": "Alice", "age": 30, "skills": ["Python", "SQL", "JavaScript"]}'),
                ('{"name": "Bob", "age": 25, "department": {"name": "Engineering", "floor": 3}}',
                 '{"name": "Bob", "age": 25, "department": {"name": "Engineering", "floor": 3}}'),
                ('{"name": "Carol", "age": 35, "projects": [{"name": "Project A", "status": "active"}, {"name": "Project B", "status": "completed"}]}',
                 '{"name": "Carol", "age": 35, "projects": [{"name": "Project A", "status": "active"}, {"name": "Project B", "status": "completed"}]}')
            """)
            cur.execute(operations[-1])

            # JSON查询操作
            operations.append("SELECT data->>'name' as name, data->>'age' as age FROM json_test")
            cur.execute(operations[-1])
            results = cur.fetchall()
            logger.info(f"JSON查询返回 {len(results)} 行")

            # JSONB操作
            operations.append("SELECT data_binary->'skills' as skills FROM json_test WHERE data_binary ? 'skills'")
            cur.execute(operations[-1])
            results = cur.fetchall()
            logger.info(f"JSONB查询返回 {len(results)} 行")

            # JSON路径查询
            operations.append("SELECT data_binary #> '{department,name}' as dept_name FROM json_test WHERE data_binary #> '{department}' IS NOT NULL")
            cur.execute(operations[-1])
            results = cur.fetchall()
            logger.info(f"JSON路径查询返回 {len(results)} 行")

            # JSON聚合
            operations.append("SELECT JSON_AGG(data_binary) as all_data FROM json_test")
            cur.execute(operations[-1])
            result = cur.fetchone()
            logger.info(f"JSON聚合结果长度: {len(str(result[0])) if result[0] else 0}")

            conn.commit()

            # 清理
            try:
                cur.execute("DROP TABLE IF EXISTS json_test")
                conn.commit()
            except:
                pass

        except Exception as e:
            logger.error(f"JSON操作测试失败: {e}")
            conn.rollback()
            raise e
        finally:
            # 关键：先关闭数据库连接，再停止抓包
            cur.close()
            conn.close()
            logger.info("数据库连接已关闭")

            time.sleep(3)  # 等待网络包传输完成
            self.stop_capture()

        return operations

    def test_transaction_control(self):
        """测试事务控制"""
        conn = psycopg2.connect(**DB_CONFIG)
        cur = conn.cursor()
        operations = []

        try:
            # 创建测试表
            operations.append("""
                CREATE TABLE transaction_test (
                    id SERIAL PRIMARY KEY,
                    name VARCHAR(100),
                    balance DECIMAL(10,2)
                )
            """)
            cur.execute(operations[-1])

            # 插入初始数据
            operations.append("""
                INSERT INTO transaction_test (name, balance) VALUES
                ('Account A', 1000.00),
                ('Account B', 500.00)
            """)
            cur.execute(operations[-1])
            conn.commit()

            # 测试事务回滚
            operations.append("BEGIN")
            cur.execute("BEGIN")

            operations.append("UPDATE transaction_test SET balance = balance - 100 WHERE name = 'Account A'")
            cur.execute(operations[-1])

            operations.append("UPDATE transaction_test SET balance = balance + 100 WHERE name = 'Account B'")
            cur.execute(operations[-1])

            # 模拟错误，回滚事务
            operations.append("ROLLBACK")
            cur.execute("ROLLBACK")

            # 测试保存点
            operations.append("BEGIN")
            cur.execute("BEGIN")

            operations.append("SAVEPOINT sp1")
            cur.execute("SAVEPOINT sp1")

            operations.append("UPDATE transaction_test SET balance = balance - 50 WHERE name = 'Account A'")
            cur.execute(operations[-1])

            operations.append("SAVEPOINT sp2")
            cur.execute("SAVEPOINT sp2")

            operations.append("UPDATE transaction_test SET balance = balance + 50 WHERE name = 'Account B'")
            cur.execute(operations[-1])

            # 回滚到保存点
            operations.append("ROLLBACK TO SAVEPOINT sp1")
            cur.execute("ROLLBACK TO SAVEPOINT sp1")

            operations.append("COMMIT")
            cur.execute("COMMIT")

            # 查询最终结果
            operations.append("SELECT * FROM transaction_test")
            cur.execute(operations[-1])
            results = cur.fetchall()
            logger.info(f"事务测试结果: {results}")


            # 清理
            try:
                cur.execute("DROP TABLE IF EXISTS transaction_test")
                conn.commit()
            except:
                pass

        except Exception as e:
            logger.error(f"事务控制测试失败: {e}")
            conn.rollback()
            raise e
        finally:
            # 关键：先关闭数据库连接，再停止抓包
            cur.close()
            conn.close()
            logger.info("数据库连接已关闭")

            time.sleep(3)  # 等待网络包传输完成
            self.stop_capture()

        return operations

    def test_advanced_features(self):
        """测试高级功能"""
        conn = psycopg2.connect(**DB_CONFIG)
        cur = conn.cursor()
        operations = []

        try:
            # 创建测试表
            operations.append("""
                CREATE TABLE advanced_test (
                    id SERIAL PRIMARY KEY,
                    name VARCHAR(100),
                    tags TEXT[],
                    metadata JSONB,
                    search_vector TSVECTOR,
                    created_at TIMESTAMP DEFAULT NOW()
                )
            """)
            cur.execute(operations[-1])

            # 插入测试数据
            operations.append("""
                INSERT INTO advanced_test (name, tags, metadata) VALUES
                ('PostgreSQL Tutorial', ARRAY['database', 'sql', 'tutorial'],
                 '{"author": "John Doe", "difficulty": "beginner", "rating": 4.5}'),
                ('Advanced SQL Queries', ARRAY['sql', 'advanced', 'queries'],
                 '{"author": "Jane Smith", "difficulty": "advanced", "rating": 4.8}'),
                ('Database Design Patterns', ARRAY['database', 'design', 'patterns'],
                 '{"author": "Bob Johnson", "difficulty": "intermediate", "rating": 4.2}')
            """)
            cur.execute(operations[-1])

            # 更新全文搜索向量
            operations.append("""
                UPDATE advanced_test
                SET search_vector = to_tsvector('english', name || ' ' || array_to_string(tags, ' '))
            """)
            cur.execute(operations[-1])

            # 数组操作测试
            operations.append("SELECT name, tags, array_length(tags, 1) as tag_count FROM advanced_test")
            cur.execute(operations[-1])
            results = cur.fetchall()
            logger.info(f"数组操作查询返回 {len(results)} 行")

            # 数组搜索
            operations.append("SELECT name FROM advanced_test WHERE 'sql' = ANY(tags)")
            cur.execute(operations[-1])
            results = cur.fetchall()
            logger.info(f"数组搜索返回 {len(results)} 行")

            # JSONB查询
            operations.append("SELECT name, metadata->>'author' as author, (metadata->>'rating')::float as rating FROM advanced_test")
            cur.execute(operations[-1])
            results = cur.fetchall()
            logger.info(f"JSONB查询返回 {len(results)} 行")

            # 全文搜索
            operations.append("SELECT name, ts_rank(search_vector, query) as rank FROM advanced_test, to_tsquery('english', 'sql') query WHERE search_vector @@ query ORDER BY rank DESC")
            cur.execute(operations[-1])
            results = cur.fetchall()
            logger.info(f"全文搜索返回 {len(results)} 行")

            # 创建索引
            operations.append("CREATE INDEX idx_advanced_tags ON advanced_test USING GIN(tags)")
            cur.execute(operations[-1])

            operations.append("CREATE INDEX idx_advanced_metadata ON advanced_test USING GIN(metadata)")
            cur.execute(operations[-1])

            operations.append("CREATE INDEX idx_advanced_search ON advanced_test USING GIN(search_vector)")
            cur.execute(operations[-1])

            conn.commit()

            # 清理
            try:
                cur.execute("DROP TABLE IF EXISTS advanced_test CASCADE")
                conn.commit()
            except:
                pass

        except Exception as e:
            logger.error(f"高级功能测试失败: {e}")
            conn.rollback()
            raise e
        finally:
            # 关键：先关闭数据库连接，再停止抓包
            cur.close()
            conn.close()
            logger.info("数据库连接已关闭")

            time.sleep(3)  # 等待网络包传输完成
            self.stop_capture()

        return operations

    def test_administrative_commands(self):
        """测试管理命令"""
        conn = psycopg2.connect(**DB_CONFIG)
        # 设置自动提交模式，这样VACUUM和ANALYZE可以正常运行
        conn.autocommit = True
        cur = conn.cursor()
        operations = []

        try:
            # 创建测试表
            operations.append("""
                CREATE TABLE admin_test (
                    id SERIAL PRIMARY KEY,
                    data TEXT
                )
            """)
            cur.execute(operations[-1])

            # 插入大量数据
            operations.append("""
                INSERT INTO admin_test (data)
                SELECT 'test data ' || generate_series(1, 1000)
            """)
            cur.execute(operations[-1])

            # VACUUM测试 - 需要在自动提交模式下运行
            operations.append("VACUUM admin_test")
            cur.execute(operations[-1])
            logger.info("VACUUM 执行成功")

            # ANALYZE测试 - 需要在自动提交模式下运行
            operations.append("ANALYZE admin_test")
            cur.execute(operations[-1])
            logger.info("ANALYZE 执行成功")

            # 查看统计信息
            operations.append("""
                SELECT schemaname, relname, n_tup_ins, n_tup_upd, n_tup_del
                FROM pg_stat_user_tables
                WHERE relname = 'admin_test'
            """)
            cur.execute(operations[-1])
            results = cur.fetchall()
            logger.info(f"表统计信息: {results}")

            # EXPLAIN测试
            operations.append("EXPLAIN (ANALYZE, BUFFERS) SELECT * FROM admin_test WHERE id < 100")
            cur.execute(operations[-1])
            results = cur.fetchall()
            logger.info(f"执行计划: {len(results)} 行")

            # 系统信息查询
            operations.append("SELECT version()")
            cur.execute(operations[-1])
            result = cur.fetchone()
            logger.info(f"数据库版本: {result[0][:100]}...")

            operations.append("SELECT current_database(), current_user, session_user")
            cur.execute(operations[-1])
            result = cur.fetchone()
            logger.info(f"会话信息: {result}")

            # 查看数据库大小
            operations.append("SELECT pg_size_pretty(pg_database_size(current_database()))")
            cur.execute(operations[-1])
            result = cur.fetchone()
            logger.info(f"数据库大小: {result[0]}")

            # 查看表大小
            operations.append("SELECT pg_size_pretty(pg_total_relation_size('admin_test'))")
            cur.execute(operations[-1])
            result = cur.fetchone()
            logger.info(f"表大小: {result[0]}")

            # 清理
            cur.execute("DROP TABLE IF EXISTS admin_test")

        except Exception as e:
            logger.error(f"管理命令测试失败: {e}")
            raise e
        finally:
            # 关键：先关闭数据库连接，再停止抓包
            cur.close()
            conn.close()
            logger.info("数据库连接已关闭")

            time.sleep(3)  # 等待网络包传输完成
            self.stop_capture()

        return operations

    def test_error_scenarios(self):
        """测试错误场景"""
        conn = psycopg2.connect(**DB_CONFIG)
        cur = conn.cursor()
        operations = []

        try:
            # 语法错误
            try:
                operations.append("SELECT * FORM non_existent_table")  # 故意的语法错误
                cur.execute(operations[-1])
            except psycopg2.Error as e:
                logger.info(f"语法错误捕获: {e}")
                conn.rollback()

            # 表不存在错误
            try:
                operations.append("SELECT * FROM definitely_not_exists")
                cur.execute(operations[-1])
            except psycopg2.Error as e:
                logger.info(f"表不存在错误捕获: {e}")
                conn.rollback()

            # 数据类型错误
            try:
                operations.append("SELECT 'text' + 123")
                cur.execute(operations[-1])
            except psycopg2.Error as e:
                logger.info(f"数据类型错误捕获: {e}")
                conn.rollback()

            # 约束违反错误
            operations.append("""
                CREATE TABLE constraint_test (
                    id INTEGER PRIMARY KEY,
                    email VARCHAR(100) UNIQUE NOT NULL
                )
            """)
            cur.execute(operations[-1])

            operations.append("INSERT INTO constraint_test VALUES (1, '<EMAIL>')")
            cur.execute(operations[-1])

            try:
                operations.append("INSERT INTO constraint_test VALUES (2, '<EMAIL>')")  # 重复邮箱
                cur.execute(operations[-1])
            except psycopg2.Error as e:
                logger.info(f"唯一约束违反错误捕获: {e}")
                conn.rollback()

            # 除零错误
            try:
                operations.append("SELECT 1/0")
                cur.execute(operations[-1])
            except psycopg2.Error as e:
                logger.info(f"除零错误捕获: {e}")
                conn.rollback()


            # 清理
            try:
                cur.execute("DROP TABLE IF EXISTS constraint_test")
                conn.commit()
            except:
                pass

        except Exception as e:
            logger.error(f"错误场景测试失败: {e}")
            conn.rollback()
            raise e
        finally:
            # 关键：先关闭数据库连接，再停止抓包
            cur.close()
            conn.close()
            logger.info("数据库连接已关闭")

            time.sleep(3)  # 等待网络包传输完成
            self.stop_capture()

        return operations

    def generate_comprehensive_report(self):
        """生成全面的测试报告"""
        report_content = f"""# PostgreSQL 全面流量抓包测试报告

## 测试概要
- 测试时间: {datetime.now().strftime('%Y-%m-%d %H:%M:%S')}
- 测试用例总数: {len(self.test_results)}
- 成功测试: {len([r for r in self.test_results if r['status'] == 'SUCCESS'])}
- 失败测试: {len([r for r in self.test_results if r['status'] == 'FAILED'])}

## 测试结果详情

"""

        for result in self.test_results:
            report_content += f"""### {result['test_name']}
- 状态: {result['status']}
- 时间: {result['timestamp']}
- PCAP文件: {os.path.basename(result['pcap_file'])}
- 操作数量: {len(result['operations'])}

"""
            if result['status'] == 'FAILED':
                report_content += f"- 错误信息: {result.get('error', 'Unknown error')}\n\n"
            else:
                report_content += f"- 执行的操作:\n"
                for i, op in enumerate(result['operations'][:5], 1):  # 只显示前5个操作
                    report_content += f"  {i}. {op[:100]}{'...' if len(op) > 100 else ''}\n"
                if len(result['operations']) > 5:
                    report_content += f"  ... 还有 {len(result['operations']) - 5} 个操作\n"
                report_content += "\n"

        # 保存报告
        report_file = os.path.join(CAPTURE_CONFIG['local_dir'],
                                 f"comprehensive_test_report_{datetime.now().strftime('%Y%m%d_%H%M%S')}.md")
        with open(report_file, 'w', encoding='utf-8') as f:
            f.write(report_content)

        logger.info(f"测试报告已生成: {report_file}")
        return report_file

    def cleanup(self):
        """清理资源"""
        if self.sftp:
            self.sftp.close()
        if self.ssh:
            self.ssh.close()

def main():
    """主函数"""
    capture = ComprehensiveTrafficCapture()

    try:
        # 定义所有测试用例
        test_cases = [
            ("DDL操作", capture.test_ddl_operations),
            ("数据类型", capture.test_data_types),
            ("DML操作", capture.test_dml_operations),
            ("复杂查询", capture.test_complex_queries),
            ("函数和操作符", capture.test_functions_and_operators),
            ("JSON操作", capture.test_json_operations),
            ("事务控制", capture.test_transaction_control),
            ("高级功能", capture.test_advanced_features),
            ("管理命令", capture.test_administrative_commands),
            ("错误场景", capture.test_error_scenarios)
        ]

        logger.info(f"开始执行 {len(test_cases)} 个测试用例")

        # 执行所有测试
        for test_name, test_func in test_cases:
            try:
                capture.execute_test_with_capture(test_name, test_func)
                time.sleep(2)  # 测试间隔
            except Exception as e:
                logger.error(f"测试 {test_name} 执行失败: {e}")
                continue

        # 生成报告
        report_file = capture.generate_comprehensive_report()
        logger.info(f"\n所有测试完成！")
        logger.info(f"测试报告: {report_file}")
        logger.info(f"PCAP文件保存在: {CAPTURE_CONFIG['local_dir']}")

    finally:
        capture.cleanup()

if __name__ == "__main__":
    main()
