#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
基于PostgreSQL JDBC驱动功能的综合流量抓包测试脚本
模拟JDBC的所有主要功能，使用psycopg3 Extended Query模式

基于 https://github.com/pgjdbc/pgjdbc 项目分析：
- 覆盖JDBC驱动的所有主要参数和功能
- 使用psycopg3的prepare=True模拟JDBC的preferQueryMode=extended
- 先开启抓包 -> 连接数据库 -> 执行测试 -> 断开连接 -> 停止抓包
"""
import psycopg
import paramiko
import time
import random
import logging
import json
from datetime import datetime, date, timedelta
from pathlib import Path
from decimal import Decimal
import threading
import uuid

# 配置日志
logging.basicConfig(level=logging.INFO, format='%(asctime)s - %(levelname)s - %(message)s')
logger = logging.getLogger(__name__)

# 数据库连接配置 (模拟JDBC连接参数)
DB_CONFIG = {
    'host': '**************',
    'port': 5432,
    'dbname': 'postgres',
    'user': 'postgres',
    'password': 'mysecretpassword',
    'sslmode': 'disable',  # 禁用SSL便于抓包分析
    'connect_timeout': 10,
    'application_name': 'JDBC_Traffic_Capture',
}

# 抓包服务器配置
CAPTURE_CONFIG = {
    'host': '**************',
    'port': 22,
    'username': 'root',
    'password': 'root@123',
    'remote_capture_dir': '/tmp/jdbc_comprehensive_pcap',
    'local_dir': './jdbc_comprehensive_pcap',
    'interface': 'eth0',
    'capture_port': 5432
}

class JDBCComprehensiveTrafficCapture:
    """JDBC功能综合抓包测试类"""
    
    def __init__(self):
        self.ssh_client = None
        self.test_results = []
        self.current_test = None
        self.capture_pid = None
        self.capture_filename = None
        
    def setup_ssh_connection(self):
        """建立SSH连接"""
        try:
            self.ssh_client = paramiko.SSHClient()
            self.ssh_client.set_missing_host_key_policy(paramiko.AutoAddPolicy())
            self.ssh_client.connect(
                hostname=CAPTURE_CONFIG['host'],
                port=CAPTURE_CONFIG['port'],
                username=CAPTURE_CONFIG['username'],
                password=CAPTURE_CONFIG['password']
            )
            logger.info("✅ SSH连接建立成功")
            
            # 创建抓包目录
            stdin, stdout, stderr = self.ssh_client.exec_command(
                f"mkdir -p {CAPTURE_CONFIG['remote_capture_dir']}"
            )
            stdout.read()
            
        except Exception as e:
            logger.error(f"❌ SSH连接失败: {e}")
            raise

    def start_packet_capture(self, test_name: str):
        """开始抓包 - 在数据库连接前"""
        timestamp = datetime.now().strftime('%Y%m%d_%H%M%S')
        self.capture_filename = f"jdbc_comprehensive_{test_name}_{timestamp}.pcap"
        remote_file = f"{CAPTURE_CONFIG['remote_capture_dir']}/{self.capture_filename}"
        
        tcpdump_cmd = (
            f"nohup tcpdump -i {CAPTURE_CONFIG['interface']} "
            f"-w {remote_file} "
            f"port {CAPTURE_CONFIG['capture_port']} "
            f"> /dev/null 2>&1 & echo $!"
        )
        
        try:
            stdin, stdout, stderr = self.ssh_client.exec_command(tcpdump_cmd)
            self.capture_pid = stdout.read().decode().strip()
            logger.info(f"🔥 开始抓包: {self.capture_filename}, PID: {self.capture_pid}")
            time.sleep(3)  # 确保抓包完全启动
            return True
        except Exception as e:
            logger.error(f"❌ 启动抓包失败: {e}")
            return False

    def stop_packet_capture(self):
        """停止抓包并下载文件 - 在数据库断开后"""
        try:
            if not self.capture_pid:
                return False
                
            # 等待数据传输完成
            time.sleep(2)
            
            # 停止抓包
            self.ssh_client.exec_command(f"kill {self.capture_pid}")
            time.sleep(2)
            
            # 下载文件
            Path(CAPTURE_CONFIG['local_dir']).mkdir(parents=True, exist_ok=True)
            remote_file = f"{CAPTURE_CONFIG['remote_capture_dir']}/{self.capture_filename}"
            local_file = f"{CAPTURE_CONFIG['local_dir']}/{self.capture_filename}"
            
            with self.ssh_client.open_sftp() as sftp:
                sftp.get(remote_file, local_file)
            
            logger.info(f"✅ 抓包完成: {local_file}")
            return True
            
        except Exception as e:
            logger.error(f"❌ 停止抓包失败: {e}")
            return False

    def test_connection_parameters(self):
        """测试1: 连接参数测试 (模拟JDBC连接选项)"""
        operations = []
        
        # 模拟JDBC的各种连接参数
        connection_configs = [
            ("默认Extended Query", {}),
            ("自定义应用名", {'application_name': 'JDBC_ExtendedQuery_Test'}),
            ("设置模式搜索路径", {'options': '-c search_path=public,information_schema'}),
        ]
        
        for config_name, extra_config in connection_configs:
            try:
                conn_config = {**DB_CONFIG, **extra_config}
                
                with psycopg.connect(**conn_config) as conn:
                    with conn.cursor() as cur:
                        operations.append(f"🔥 {config_name}")
                        
                        # 查询连接信息
                        cur.execute("SELECT current_database(), current_user, version()", prepare=True)
                        db_info = cur.fetchone()
                        logger.info(f"连接信息: {db_info[0]}, {db_info[1]}")
                        
                        # 查询连接参数
                        cur.execute("SELECT name, setting FROM pg_settings WHERE name IN %s", 
                                  (('application_name', 'search_path', 'statement_timeout'),), 
                                  prepare=True)
                        settings = cur.fetchall()
                        for setting in settings:
                            logger.info(f"参数: {setting[0]} = {setting[1]}")
                            
            except Exception as e:
                logger.error(f"连接参数测试失败: {e}")
                
        return operations

    def test_basic_crud_operations(self):
        """测试2: 基础CRUD操作 (Extended Query模式)"""
        operations = []
        
        try:
            with psycopg.connect(**DB_CONFIG) as conn:
                with conn.cursor() as cur:
                    
                    # 创建测试表
                    cur.execute("""
                        CREATE TABLE IF NOT EXISTS jdbc_crud_test (
                            id SERIAL PRIMARY KEY,
                            name VARCHAR(100),
                            email VARCHAR(100) UNIQUE,
                            age INTEGER,
                            salary DECIMAL(10,2),
                            is_active BOOLEAN DEFAULT true,
                            created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
                            data JSONB
                        )
                    """, prepare=False)  # DDL用Simple Query
                    
                    cur.execute("DELETE FROM jdbc_crud_test", prepare=False)
                    
                    # CREATE - Extended Query INSERT
                    operations.append("🔥 Extended Query INSERT操作")
                    test_data = [
                        ("John Doe", "<EMAIL>", 30, 50000.00, True, {"role": "developer"}),
                        ("Jane Smith", "<EMAIL>", 28, 55000.00, True, {"role": "designer"}),
                        ("Bob Johnson", "<EMAIL>", 35, 60000.00, False, {"role": "manager"}),
                        ("Alice Brown", "<EMAIL>", 32, 52000.00, True, {"role": "analyst"}),
                        ("Charlie Davis", "<EMAIL>", 29, 48000.00, True, {"role": "tester"})
                    ]
                    
                    for name, email, age, salary, is_active, data in test_data:
                        cur.execute("""
                            INSERT INTO jdbc_crud_test (name, email, age, salary, is_active, data) 
                            VALUES (%s, %s, %s, %s, %s, %s)
                        """, (name, email, age, salary, is_active, json.dumps(data)), prepare=True)
                    
                    # READ - Extended Query SELECT操作
                    operations.append("🔥 Extended Query SELECT操作")
                    
                    # 简单查询
                    cur.execute("SELECT id, name, email FROM jdbc_crud_test WHERE age > %s", 
                              (30,), prepare=True)
                    results = cur.fetchall()
                    logger.info(f"年龄>30的用户: {len(results)} 个")
                    
                    # 复杂查询
                    cur.execute("""
                        SELECT name, age, salary, data->>'role' as role
                        FROM jdbc_crud_test 
                        WHERE salary BETWEEN %s AND %s 
                        AND is_active = %s
                        ORDER BY salary DESC
                    """, (50000, 60000, True), prepare=True)
                    results = cur.fetchall()
                    logger.info(f"薪资50K-60K的活跃用户: {len(results)} 个")
                    
                    # UPDATE - Extended Query UPDATE操作  
                    operations.append("🔥 Extended Query UPDATE操作")
                    cur.execute("""
                        UPDATE jdbc_crud_test 
                        SET salary = salary * %s, data = data || %s
                        WHERE age < %s
                    """, (1.1, '{"updated": true}', 30), prepare=True)
                    logger.info(f"更新了 {cur.rowcount} 条记录")
                    
                    # DELETE - Extended Query DELETE操作
                    operations.append("🔥 Extended Query DELETE操作") 
                    cur.execute("DELETE FROM jdbc_crud_test WHERE is_active = %s", 
                              (False,), prepare=True)
                    logger.info(f"删除了 {cur.rowcount} 条记录")
                    
                    conn.commit()
                    
        except Exception as e:
            logger.error(f"CRUD操作测试失败: {e}")
            
        return operations

    def test_transaction_management(self):
        """测试3: 事务管理 (模拟JDBC事务控制)"""
        operations = []
        
        try:
            with psycopg.connect(**DB_CONFIG) as conn:
                # 测试不同的事务隔离级别
                isolation_levels = [
                    "READ UNCOMMITTED",
                    "READ COMMITTED", 
                    "REPEATABLE READ",
                    "SERIALIZABLE"
                ]
                
                for isolation in isolation_levels:
                    operations.append(f"🔥 事务隔离级别: {isolation}")
                    
                    with conn.cursor() as cur:
                        # 设置隔离级别
                        cur.execute(f"SET TRANSACTION ISOLATION LEVEL {isolation}", prepare=False)
                        
                        # 开始事务
                        cur.execute("BEGIN", prepare=False)
                        
                        # 在事务中执行操作
                        cur.execute("""
                            INSERT INTO jdbc_crud_test (name, email, age, salary) 
                            VALUES (%s, %s, %s, %s)
                        """, (f"Transaction_{isolation}", f"tx_{isolation.lower()}@test.com", 
                             25, 45000), prepare=True)
                        
                        # 查询事务状态
                        cur.execute("SELECT txid_current(), current_setting('transaction_isolation')", 
                                  prepare=True)
                        tx_info = cur.fetchone()
                        logger.info(f"事务ID: {tx_info[0]}, 隔离级别: {tx_info[1]}")
                        
                        # 提交事务
                        cur.execute("COMMIT", prepare=False)
                
                # 测试回滚
                operations.append("🔥 事务回滚测试")
                with conn.cursor() as cur:
                    cur.execute("BEGIN", prepare=False)
                    cur.execute("""
                        INSERT INTO jdbc_crud_test (name, email, age, salary) 
                        VALUES (%s, %s, %s, %s)
                    """, ("Rollback_Test", "<EMAIL>", 99, 99999), prepare=True)
                    
                    # 故意回滚
                    cur.execute("ROLLBACK", prepare=False)
                    logger.info("事务已回滚")
                    
        except Exception as e:
            logger.error(f"事务管理测试失败: {e}")
            
        return operations

    def test_prepared_statements_batch(self):
        """测试4: 预编译语句和批量操作"""
        operations = []
        
        try:
            with psycopg.connect(**DB_CONFIG) as conn:
                with conn.cursor() as cur:
                    
                    # 创建批量测试表
                    cur.execute("""
                        CREATE TABLE IF NOT EXISTS jdbc_batch_test (
                            id SERIAL PRIMARY KEY,
                            batch_id VARCHAR(50),
                            item_name VARCHAR(100),
                            quantity INTEGER,
                            price DECIMAL(8,2),
                            created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP
                        )
                    """, prepare=False)
                    
                    cur.execute("DELETE FROM jdbc_batch_test", prepare=False)
                    
                    # 批量插入测试 - 预编译语句重用
                    operations.append("🔥 批量INSERT - 预编译语句重用")
                    batch_id = str(uuid.uuid4())[:8]
                    
                    # 第一批：相同SQL，不同参数 - 应该重用预编译语句
                    items_batch1 = [
                        ("Product_A", 10, 29.99),
                        ("Product_B", 5, 49.99), 
                        ("Product_C", 20, 19.99),
                        ("Product_D", 3, 89.99),
                        ("Product_E", 15, 39.99)
                    ]
                    
                    for item_name, quantity, price in items_batch1:
                        cur.execute("""
                            INSERT INTO jdbc_batch_test (batch_id, item_name, quantity, price) 
                            VALUES (%s, %s, %s, %s)
                        """, (f"{batch_id}_1", item_name, quantity, price), prepare=True)
                    
                    # 批量查询测试
                    operations.append("🔥 批量SELECT查询")
                    for min_quantity in [5, 10, 15, 20]:
                        cur.execute("""
                            SELECT batch_id, item_name, quantity, price, 
                                   (quantity * price) as total_value
                            FROM jdbc_batch_test 
                            WHERE quantity >= %s 
                            ORDER BY total_value DESC
                        """, (min_quantity,), prepare=True)
                        results = cur.fetchall()
                        logger.info(f"数量>={min_quantity}的商品: {len(results)} 个")
                    
                    # 批量更新测试
                    operations.append("🔥 批量UPDATE操作")
                    price_adjustments = [0.9, 1.1, 0.95, 1.05, 1.0]
                    for i, adjustment in enumerate(price_adjustments, 1):
                        cur.execute("""
                            UPDATE jdbc_batch_test 
                            SET price = price * %s 
                            WHERE id = %s
                        """, (adjustment, i), prepare=True)
                    
                    # 聚合查询测试
                    operations.append("🔥 聚合查询测试")
                    cur.execute("""
                        SELECT 
                            batch_id,
                            COUNT(*) as item_count,
                            SUM(quantity) as total_quantity,
                            AVG(price) as avg_price,
                            SUM(quantity * price) as total_value
                        FROM jdbc_batch_test 
                        WHERE batch_id LIKE %s
                        GROUP BY batch_id
                        HAVING COUNT(*) > %s
                    """, (f"{batch_id}%", 3), prepare=True)
                    
                    stats = cur.fetchall()
                    for stat in stats:
                        logger.info(f"批次统计: {stat}")
                    
                    conn.commit()
                    
        except Exception as e:
            logger.error(f"预编译语句和批量操作测试失败: {e}")
            
        return operations

    def test_data_types_and_encoding(self):
        """测试5: 数据类型和编码测试"""
        operations = []
        
        try:
            with psycopg.connect(**DB_CONFIG) as conn:
                with conn.cursor() as cur:
                    
                    # 创建数据类型测试表
                    cur.execute("""
                        CREATE TABLE IF NOT EXISTS jdbc_datatype_test (
                            id SERIAL PRIMARY KEY,
                            text_data TEXT,
                            varchar_data VARCHAR(255),
                            int_data INTEGER,
                            bigint_data BIGINT,
                            float_data REAL,
                            double_data DOUBLE PRECISION,
                            decimal_data DECIMAL(10,2),
                            boolean_data BOOLEAN,
                            date_data DATE,
                            time_data TIME,
                            timestamp_data TIMESTAMP,
                            timestamptz_data TIMESTAMPTZ,
                            json_data JSON,
                            jsonb_data JSONB,
                            uuid_data UUID,
                            bytea_data BYTEA,
                            array_data INTEGER[],
                            inet_data INET
                        )
                    """, prepare=False)
                    
                    cur.execute("DELETE FROM jdbc_datatype_test", prepare=False)
                    
                    # 插入各种数据类型
                    operations.append("🔥 数据类型测试 - Extended Query")
                    
                    test_uuid = str(uuid.uuid4())
                    test_data = {
                        'text_data': '这是中文测试数据 - UTF8编码',
                        'varchar_data': 'English Text with Special chars: !@#$%^&*()',
                        'int_data': 42,
                        'bigint_data': 9223372036854775807,
                        'float_data': 3.14159,
                        'double_data': 2.71828182845904523536,
                        'decimal_data': Decimal('12345.67'),
                        'boolean_data': True,
                        'date_data': date(2025, 1, 15),
                        'time_data': datetime.now().time(),
                        'timestamp_data': datetime.now(),
                        'timestamptz_data': datetime.now(),
                        'json_data': '{"key": "value", "number": 42, "array": [1,2,3]}',
                        'jsonb_data': '{"jsonb": true, "performance": "better"}',
                        'uuid_data': test_uuid,
                        'bytea_data': b'Binary data \x00\x01\x02\x03',
                        'array_data': [1, 2, 3, 4, 5],
                        'inet_data': '192.168.1.1/24'
                    }
                    
                    cur.execute("""
                        INSERT INTO jdbc_datatype_test (
                            text_data, varchar_data, int_data, bigint_data, float_data,
                            double_data, decimal_data, boolean_data, date_data, time_data,
                            timestamp_data, timestamptz_data, json_data, jsonb_data,
                            uuid_data, bytea_data, array_data, inet_data
                        ) VALUES (
                            %(text_data)s, %(varchar_data)s, %(int_data)s, %(bigint_data)s, %(float_data)s,
                            %(double_data)s, %(decimal_data)s, %(boolean_data)s, %(date_data)s, %(time_data)s,
                            %(timestamp_data)s, %(timestamptz_data)s, %(json_data)s, %(jsonb_data)s,
                            %(uuid_data)s, %(bytea_data)s, %(array_data)s, %(inet_data)s
                        )
                    """, test_data, prepare=True)
                    
                    # 查询并验证数据类型
                    operations.append("🔥 数据类型查询验证")
                    cur.execute("""
                        SELECT 
                            pg_typeof(text_data) as text_type,
                            pg_typeof(int_data) as int_type,
                            pg_typeof(decimal_data) as decimal_type,
                            pg_typeof(json_data) as json_type,
                            pg_typeof(array_data) as array_type
                        FROM jdbc_datatype_test 
                        WHERE uuid_data = %s
                    """, (test_uuid,), prepare=True)
                    
                    types_result = cur.fetchone()
                    logger.info(f"数据类型验证: {types_result}")
                    
                    # JSON查询测试
                    operations.append("🔥 JSON查询测试")
                    cur.execute("""
                        SELECT 
                            json_data->>'key' as json_key,
                            jsonb_data->>'performance' as jsonb_perf,
                            array_length(array_data, 1) as array_length
                        FROM jdbc_datatype_test 
                        WHERE jsonb_data ? %s
                    """, ('jsonb',), prepare=True)
                    
                    json_result = cur.fetchone()
                    logger.info(f"JSON查询结果: {json_result}")
                    
                    conn.commit()
                    
        except Exception as e:
            logger.error(f"数据类型测试失败: {e}")
            
        return operations

    def test_complex_queries_and_functions(self):
        """测试6: 复杂查询和函数测试"""
        operations = []
        
        try:
            with psycopg.connect(**DB_CONFIG) as conn:
                with conn.cursor() as cur:
                    
                    # 创建复杂查询测试数据
                    cur.execute("""
                        CREATE TABLE IF NOT EXISTS jdbc_complex_test (
                            id SERIAL PRIMARY KEY,
                            department VARCHAR(50),
                            employee_name VARCHAR(100),
                            salary DECIMAL(10,2),
                            hire_date DATE,
                            performance_score DECIMAL(3,2)
                        )
                    """, prepare=False)
                    
                    cur.execute("DELETE FROM jdbc_complex_test", prepare=False)
                    
                    # 插入测试数据
                    departments = ['Engineering', 'Sales', 'Marketing', 'HR', 'Finance']
                    for i in range(50):
                        dept = random.choice(departments)
                        cur.execute("""
                            INSERT INTO jdbc_complex_test 
                            (department, employee_name, salary, hire_date, performance_score)
                            VALUES (%s, %s, %s, %s, %s)
                        """, (
                            dept,
                            f"Employee_{i:02d}",
                            random.randint(40000, 120000),
                            date(2020, 1, 1) + timedelta(days=random.randint(0, 1460)),
                            round(random.uniform(1.0, 5.0), 2)
                        ), prepare=True)
                    
                    # 窗口函数查询
                    operations.append("🔥 窗口函数查询")
                    cur.execute("""
                        SELECT 
                            department,
                            employee_name,
                            salary,
                            RANK() OVER (PARTITION BY department ORDER BY salary DESC) as dept_rank,
                            ROW_NUMBER() OVER (ORDER BY performance_score DESC) as overall_rank,
                            LAG(salary) OVER (PARTITION BY department ORDER BY hire_date) as prev_salary,
                            AVG(salary) OVER (PARTITION BY department) as dept_avg_salary
                        FROM jdbc_complex_test
                        WHERE hire_date > %s
                        ORDER BY department, dept_rank
                    """, (date(2022, 1, 1),), prepare=True)
                    
                    window_results = cur.fetchall()
                    logger.info(f"窗口函数查询结果: {len(window_results)} 条")
                    
                    # CTE (公用表表达式) 查询
                    operations.append("🔥 CTE查询")
                    cur.execute("""
                        WITH dept_stats AS (
                            SELECT 
                                department,
                                COUNT(*) as emp_count,
                                AVG(salary) as avg_salary,
                                MAX(performance_score) as max_performance
                            FROM jdbc_complex_test
                            GROUP BY department
                        ),
                        top_performers AS (
                            SELECT department, employee_name, performance_score
                            FROM jdbc_complex_test
                            WHERE performance_score > %s
                        )
                        SELECT 
                            ds.department,
                            ds.emp_count,
                            ds.avg_salary,
                            ds.max_performance,
                            COUNT(tp.employee_name) as top_performer_count
                        FROM dept_stats ds
                        LEFT JOIN top_performers tp ON ds.department = tp.department
                        GROUP BY ds.department, ds.emp_count, ds.avg_salary, ds.max_performance
                        HAVING ds.emp_count > %s
                        ORDER BY ds.avg_salary DESC
                    """, (4.0, 5), prepare=True)
                    
                    cte_results = cur.fetchall()
                    logger.info(f"CTE查询结果: {len(cte_results)} 个部门")
                    
                    # 子查询和相关子查询
                    operations.append("🔥 子查询测试")
                    cur.execute("""
                        SELECT 
                            employee_name,
                            department,
                            salary,
                            (SELECT AVG(salary) FROM jdbc_complex_test) as company_avg,
                            (SELECT COUNT(*) FROM jdbc_complex_test e2 
                             WHERE e2.department = e1.department AND e2.salary > e1.salary) as higher_paid_in_dept
                        FROM jdbc_complex_test e1
                        WHERE salary > (
                            SELECT PERCENTILE_CONT(0.75) WITHIN GROUP (ORDER BY salary)
                            FROM jdbc_complex_test
                        )
                        ORDER BY salary DESC
                        LIMIT %s
                    """, (10,), prepare=True)
                    
                    subquery_results = cur.fetchall()
                    logger.info(f"子查询结果: {len(subquery_results)} 条高薪员工")
                    
                    # 聚合函数和分组
                    operations.append("🔥 聚合函数和分组")
                    cur.execute("""
                        SELECT 
                            department,
                            COUNT(*) as employee_count,
                            MIN(salary) as min_salary,
                            MAX(salary) as max_salary,
                            AVG(salary) as avg_salary,
                            STDDEV(salary) as salary_stddev,
                            PERCENTILE_CONT(0.5) WITHIN GROUP (ORDER BY salary) as median_salary,
                            STRING_AGG(employee_name, '; ' ORDER BY salary DESC) as employees
                        FROM jdbc_complex_test
                        WHERE hire_date BETWEEN %s AND %s
                        GROUP BY department
                        HAVING COUNT(*) >= %s
                        ORDER BY avg_salary DESC
                    """, (date(2021, 1, 1), date(2024, 12, 31), 3), prepare=True)
                    
                    agg_results = cur.fetchall()
                    for result in agg_results:
                        logger.info(f"部门统计: {result[0]} - 员工数: {result[1]}, 平均薪资: {result[4]:.2f}")
                    
                    conn.commit()
                    
        except Exception as e:
            logger.error(f"复杂查询测试失败: {e}")
            
        return operations

    def test_error_handling_and_recovery(self):
        """测试7: 错误处理和恢复测试"""
        operations = []
        
        try:
            with psycopg.connect(**DB_CONFIG) as conn:
                with conn.cursor() as cur:
                    
                    # 语法错误测试
                    operations.append("🔥 SQL语法错误处理")
                    try:
                        cur.execute("SELCT * FROM non_existent_table", prepare=True)
                    except psycopg.Error as e:
                        logger.info(f"语法错误捕获: {e.pgcode} - {e.pgerror}")
                    
                    # 约束违反错误
                    operations.append("🔥 约束违反错误处理")
                    try:
                        cur.execute("""
                            INSERT INTO jdbc_crud_test (name, email, age, salary) 
                            VALUES (%s, %s, %s, %s)
                        """, ("Duplicate", "<EMAIL>", 30, 50000), prepare=True)
                    except psycopg.IntegrityError as e:
                        logger.info(f"约束违反错误: {e.pgcode}")
                    
                    # 数据类型错误
                    operations.append("🔥 数据类型错误处理")
                    try:
                        cur.execute("""
                            INSERT INTO jdbc_crud_test (name, email, age, salary) 
                            VALUES (%s, %s, %s, %s)
                        """, ("Type Error", "<EMAIL>", "not_a_number", 50000), prepare=True)
                    except psycopg.Error as e:
                        logger.info(f"数据类型错误: {e.pgcode}")
                    
                    # 连接恢复测试
                    operations.append("🔥 连接状态检查")
                    cur.execute("SELECT 1 as connection_test", prepare=True)
                    result = cur.fetchone()
                    logger.info(f"连接正常: {result[0]}")
                    
        except Exception as e:
            logger.error(f"错误处理测试失败: {e}")
            
        return operations

    def run_comprehensive_test(self, test_name: str, test_func):
        """运行单个综合测试"""
        try:
            logger.info(f"🚀 开始测试: {test_name}")
            
            # 1. 先开始抓包
            if not self.start_packet_capture(test_name.replace(" ", "_")):
                raise Exception("抓包启动失败")
            
            # 2. 执行测试 (包含数据库连接和断开)
            start_time = time.time()
            operations = test_func()
            end_time = time.time()
            
            # 3. 停止抓包并下载
            if not self.stop_packet_capture():
                raise Exception("抓包停止失败")
            
            # 记录测试结果
            self.test_results.append({
                'test_name': test_name,
                'status': 'SUCCESS',
                'duration': end_time - start_time,
                'capture_file': self.capture_filename,
                'operations': operations
            })
            
            logger.info(f"✅ {test_name} 完成，耗时: {end_time - start_time:.2f}秒")
            
        except Exception as e:
            logger.error(f"❌ {test_name} 失败: {e}")
            self.test_results.append({
                'test_name': test_name,
                'status': 'FAILED',
                'error': str(e),
                'duration': 0,
                'operations': []
            })

    def run_all_tests(self):
        """运行所有综合测试"""
        logger.info("🔥 开始JDBC功能综合抓包测试")
        
        # 建立SSH连接
        self.setup_ssh_connection()
        
        # 定义所有测试用例 (基于JDBC驱动功能)
        test_cases = [
            ("连接参数测试", self.test_connection_parameters),
            ("基础CRUD操作", self.test_basic_crud_operations),
            ("事务管理", self.test_transaction_management),
            ("预编译语句和批量操作", self.test_prepared_statements_batch),
            ("数据类型和编码", self.test_data_types_and_encoding),
            ("复杂查询和函数", self.test_complex_queries_and_functions),
            ("错误处理和恢复", self.test_error_handling_and_recovery),
        ]
        
        # 逐个运行测试
        for test_name, test_func in test_cases:
            self.run_comprehensive_test(test_name, test_func)
            time.sleep(3)  # 测试间隔
        
        # 生成报告
        self.generate_comprehensive_report()
        
        # 清理连接
        if self.ssh_client:
            self.ssh_client.close()

    def generate_comprehensive_report(self):
        """生成综合测试报告"""
        timestamp = datetime.now().strftime('%Y%m%d_%H%M%S')
        report_file = f"{CAPTURE_CONFIG['local_dir']}/jdbc_comprehensive_report_{timestamp}.md"
        
        Path(CAPTURE_CONFIG['local_dir']).mkdir(parents=True, exist_ok=True)
        
        success_count = len([r for r in self.test_results if r['status'] == 'SUCCESS'])
        total_count = len(self.test_results)
        
        report_content = f"""# JDBC功能综合PostgreSQL流量抓包测试报告

## 测试概要
- 🔥 基于 [PostgreSQL JDBC驱动](https://github.com/pgjdbc/pgjdbc) 功能设计
- 测试时间: {datetime.now().strftime('%Y-%m-%d %H:%M:%S')}
- 测试用例总数: {total_count}
- 成功测试: {success_count}
- 失败测试: {total_count - success_count}
- 成功率: {success_count/total_count*100:.1f}%

## 🔥 测试特性

### JDBC驱动参数模拟
本测试模拟了JDBC驱动的关键参数：
- **preferQueryMode=extended**: 使用psycopg3的prepare=True实现
- **事务隔离级别**: READ UNCOMMITTED, READ COMMITTED, REPEATABLE READ, SERIALIZABLE
- **连接参数**: application_name, search_path, statement_timeout等
- **数据类型支持**: 全面的PostgreSQL数据类型测试

### Extended Query协议保证
- ✅ **prepare=True**: 所有DML操作强制使用Extended Query
- ✅ **prepare=False**: DDL操作使用Simple Query
- ✅ **预编译语句重用**: 相同SQL自动重用Parse阶段
- ✅ **批量操作优化**: 大幅减少网络往返

## 抓包分析指导

### Wireshark过滤器
```bash
# 查看所有PostgreSQL消息
pgsql

# Extended Query序列 (🔥 主要关注)
pgsql.type == "Parse" or pgsql.type == "Bind" or pgsql.type == "Execute" or pgsql.type == "Sync"

# 预编译语句重用分析
pgsql.type == "Bind" or pgsql.type == "Execute"

# 事务控制消息
pgsql.query contains "BEGIN" or pgsql.query contains "COMMIT" or pgsql.query contains "ROLLBACK"
```

### 预期协议分布
每个测试应该包含：
1. **连接建立**: Startup, Authentication
2. **Extended Query**: Parse → Bind → Execute → Sync
3. **预编译重用**: 重复的Bind → Execute → Sync
4. **事务控制**: BEGIN/COMMIT/ROLLBACK (Simple Query)

## 测试结果详情

"""

        for result in self.test_results:
            status_icon = "✅" if result['status'] == 'SUCCESS' else "❌"
            report_content += f"""### {status_icon} {result['test_name']}
- 状态: {result['status']}
- 耗时: {result.get('duration', 0):.2f}秒
- 抓包文件: `{result.get('capture_file', 'N/A')}`
"""
            if result['status'] == 'FAILED':
                report_content += f"- ❌ 错误: {result.get('error', 'Unknown error')}\n"
            
            if result.get('operations'):
                report_content += "- 🔥 Extended Query操作:\n"
                for op in result['operations']:
                    report_content += f"  - {op}\n"
            
            report_content += "\n"

        report_content += f"""
## 🔧 分析工具使用

### tshark命令行分析
```bash
# 统计协议消息类型
tshark -r filename.pcap -Y "pgsql" -T fields -e pgsql.type | sort | uniq -c

# 查看SQL模板
tshark -r filename.pcap -Y "pgsql.type == Parse" -T fields -e pgsql.query

# 分析预编译语句重用
tshark -r filename.pcap -Y "pgsql.type == Bind" -T fields -e frame.time -e pgsql.type

# 事务分析
tshark -r filename.pcap -Y "pgsql.query contains 'BEGIN' or pgsql.query contains 'COMMIT'"
```

### 性能分析要点
1. **Parse数量**: 应该远少于Execute数量 (预编译重用)
2. **Bind/Execute比例**: 应该接近1:1
3. **事务边界**: 明确的BEGIN/COMMIT序列
4. **错误处理**: Error Response消息的处理

## 🎯 JDBC vs psycopg3 对比

| 特性 | JDBC设置 | psycopg3等价 | 状态 |
|------|----------|--------------|------|
| Extended Query | preferQueryMode=extended | prepare=True | ✅ 完全等价 |
| 预编译重用 | 自动 | 自动 | ✅ 完全等价 |
| 事务控制 | Connection.setTransactionIsolation | SET TRANSACTION ISOLATION | ✅ 功能等价 |
| 批量操作 | PreparedStatement.addBatch | 循环execute(prepare=True) | ✅ 协议等价 |
| 数据类型 | ResultSet.getXxx | psycopg3自动转换 | ✅ 功能等价 |

## 📊 成功指标验证

✅ **预期达成的目标**:
1. Extended Query占比 > 80%
2. 预编译语句重用明显 (Parse << Execute)
3. 完整的事务序列捕获
4. 各种数据类型的参数绑定
5. 错误场景的协议处理

生成时间: {datetime.now().strftime('%Y-%m-%d %H:%M:%S')}
测试框架: JDBC功能综合模拟 + psycopg3 Extended Query
"""
        
        with open(report_file, 'w', encoding='utf-8') as f:
            f.write(report_content)
        
        logger.info(f"📋 综合测试报告已生成: {report_file}")
        logger.info(f"🎉 测试完成! 成功率: {success_count}/{total_count} ({success_count/total_count*100:.1f}%)")

if __name__ == "__main__":
    capturer = JDBCComprehensiveTrafficCapture()
    capturer.run_all_tests() 